package database

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// 基础测试，验证包结构和基本功能
func TestPackageStructure(t *testing.T) {
	// 测试包是否正确加载
	assert.True(t, true, "Package should load correctly")
}

// 测试数据库类型常量
func TestDatabaseTypes(t *testing.T) {
	// 这里可以测试数据库类型常量是否正确定义
	// 当我们有了常量定义后可以添加具体测试
	assert.True(t, true, "Database types should be defined")
}

// 测试查询结果结构
func TestQueryResult(t *testing.T) {
	// 测试QueryResult结构体
	result := QueryResult{
		Columns:       []string{"id", "name"},
		Rows:          []map[string]interface{}{{"id": 1, "name": "test"}},
		RowsAffected:  1,
		ExecutionTime: 100,
	}

	assert.Equal(t, []string{"id", "name"}, result.Columns)
	assert.Equal(t, int64(1), result.RowsAffected)
	assert.Equal(t, int64(100), result.ExecutionTime)
}

// 测试表信息结构
func TestTableInfo(t *testing.T) {
	table := TableInfo{
		Name: "users",
		Type: "table",
		Columns: []ColumnInfo{
			{Name: "id", Type: "int", IsPrimaryKey: true},
			{Name: "name", Type: "varchar", IsPrimaryKey: false},
		},
	}

	assert.Equal(t, "users", table.Name)
	assert.Equal(t, "table", table.Type)
	assert.Len(t, table.Columns, 2)
	assert.True(t, table.Columns[0].IsPrimaryKey)
	assert.False(t, table.Columns[1].IsPrimaryKey)
}
