package tests

import (
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// BenchmarkRedisOperations Redis操作性能基准测试
func BenchmarkRedisOperations(b *testing.B) {
	server, err := miniredis.Run()
	require.NoError(b, err)
	defer server.Close()

	b.ResetTimer()

	b.Run("SET", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench_key_%d", i)
			value := fmt.Sprintf("bench_value_%d", i)
			server.Set(key, value)
		}
	})

	b.Run("GET", func(b *testing.B) {
		// 预先设置一些数据
		for i := 0; i < 1000; i++ {
			key := fmt.Sprintf("bench_key_%d", i)
			value := fmt.Sprintf("bench_value_%d", i)
			server.Set(key, value)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench_key_%d", i%1000)
			server.Get(key)
		}
	})

	b.Run("DEL", func(b *testing.B) {
		// 预先设置一些数据
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench_del_key_%d", i)
			value := fmt.Sprintf("bench_del_value_%d", i)
			server.Set(key, value)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench_del_key_%d", i)
			server.Del(key)
		}
	})
}

// BenchmarkConcurrentOperations 并发操作性能基准测试
func BenchmarkConcurrentOperations(b *testing.B) {
	server, err := miniredis.Run()
	require.NoError(b, err)
	defer server.Close()

	b.ResetTimer()

	b.Run("ConcurrentSET", func(b *testing.B) {
		var wg sync.WaitGroup
		workers := 10
		opsPerWorker := b.N / workers

		for w := 0; w < workers; w++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()
				for i := 0; i < opsPerWorker; i++ {
					key := fmt.Sprintf("concurrent_key_%d_%d", workerID, i)
					value := fmt.Sprintf("concurrent_value_%d_%d", workerID, i)
					server.Set(key, value)
				}
			}(w)
		}
		wg.Wait()
	})

	b.Run("ConcurrentGET", func(b *testing.B) {
		// 预先设置数据
		for i := 0; i < 1000; i++ {
			key := fmt.Sprintf("concurrent_get_key_%d", i)
			value := fmt.Sprintf("concurrent_get_value_%d", i)
			server.Set(key, value)
		}

		var wg sync.WaitGroup
		workers := 10
		opsPerWorker := b.N / workers

		b.ResetTimer()
		for w := 0; w < workers; w++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()
				for i := 0; i < opsPerWorker; i++ {
					key := fmt.Sprintf("concurrent_get_key_%d", i%1000)
					server.Get(key)
				}
			}(w)
		}
		wg.Wait()
	})
}

// TestMemoryPerformance 内存性能测试
func TestMemoryPerformance(t *testing.T) {
	server, err := miniredis.Run()
	require.NoError(t, err)
	defer server.Close()

	// 获取初始内存统计
	var m1 runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m1)

	// 执行大量操作
	numOperations := 100000
	start := time.Now()

	for i := 0; i < numOperations; i++ {
		key := fmt.Sprintf("memory_perf_key_%d", i)
		value := fmt.Sprintf("memory_perf_value_%d", i)
		server.Set(key, value)
	}

	duration := time.Since(start)

	// 获取最终内存统计
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)

	// 性能断言
	assert.True(t, duration < 10*time.Second, "100k operations should complete within 10 seconds")

	// 内存使用断言
	memoryIncrease := m2.Alloc - m1.Alloc
	t.Logf("Memory increase: %d bytes", memoryIncrease)
	t.Logf("Operations per second: %.2f", float64(numOperations)/duration.Seconds())
	t.Logf("Average memory per operation: %.2f bytes", float64(memoryIncrease)/float64(numOperations))

	// 清理并验证内存释放
	server.FlushAll()
	runtime.GC()
	var m3 runtime.MemStats
	runtime.ReadMemStats(&m3)

	// 验证内存有所释放（不一定完全释放，因为Go的GC策略）
	assert.True(t, m3.Alloc <= m2.Alloc, "Memory should not increase after cleanup")
}

// TestResponseTime 响应时间测试
func TestResponseTime(t *testing.T) {
	server, err := miniredis.Run()
	require.NoError(t, err)
	defer server.Close()

	// 测试单个操作的响应时间
	numTests := 1000
	var totalDuration time.Duration

	for i := 0; i < numTests; i++ {
		key := fmt.Sprintf("response_time_key_%d", i)
		value := fmt.Sprintf("response_time_value_%d", i)

		start := time.Now()
		server.Set(key, value)
		duration := time.Since(start)
		totalDuration += duration
	}

	averageResponseTime := totalDuration / time.Duration(numTests)
	t.Logf("Average response time: %v", averageResponseTime)

	// 响应时间应该在合理范围内（小于1毫秒）
	assert.True(t, averageResponseTime < time.Millisecond, "Average response time should be less than 1ms")
}

// TestThroughput 吞吐量测试
func TestThroughput(t *testing.T) {
	server, err := miniredis.Run()
	require.NoError(t, err)
	defer server.Close()

	// 测试1秒内能完成多少操作
	duration := time.Second
	start := time.Now()
	operations := 0

	for time.Since(start) < duration {
		key := fmt.Sprintf("throughput_key_%d", operations)
		value := fmt.Sprintf("throughput_value_%d", operations)
		server.Set(key, value)
		operations++
	}

	actualDuration := time.Since(start)
	throughput := float64(operations) / actualDuration.Seconds()

	t.Logf("Operations completed: %d", operations)
	t.Logf("Actual duration: %v", actualDuration)
	t.Logf("Throughput: %.2f ops/sec", throughput)

	// 吞吐量应该达到一定标准（至少10000 ops/sec）
	assert.True(t, throughput > 10000, "Throughput should be at least 10k ops/sec")
}

// TestScalability 可扩展性测试
func TestScalability(t *testing.T) {
	server, err := miniredis.Run()
	require.NoError(t, err)
	defer server.Close()

	// 测试不同数据量下的性能
	dataSizes := []int{1000, 10000, 100000}

	for _, size := range dataSizes {
		t.Run(fmt.Sprintf("DataSize_%d", size), func(t *testing.T) {
			server.FlushAll()

			start := time.Now()
			for i := 0; i < size; i++ {
				key := fmt.Sprintf("scale_key_%d", i)
				value := fmt.Sprintf("scale_value_%d", i)
				server.Set(key, value)
			}
			duration := time.Since(start)

			throughput := float64(size) / duration.Seconds()
			t.Logf("Size: %d, Duration: %v, Throughput: %.2f ops/sec", size, duration, throughput)

			// 验证性能不会随数据量线性下降
			assert.True(t, throughput > 1000, "Throughput should remain reasonable with larger datasets")
		})
	}
}

// TestConcurrencyLimits 并发限制测试
func TestConcurrencyLimits(t *testing.T) {
	server, err := miniredis.Run()
	require.NoError(t, err)
	defer server.Close()

	// 测试不同并发级别
	concurrencyLevels := []int{1, 10, 50, 100}

	for _, concurrency := range concurrencyLevels {
		t.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(t *testing.T) {
			var wg sync.WaitGroup
			opsPerWorker := 1000

			start := time.Now()
			for w := 0; w < concurrency; w++ {
				wg.Add(1)
				go func(workerID int) {
					defer wg.Done()
					for i := 0; i < opsPerWorker; i++ {
						key := fmt.Sprintf("conc_limit_key_%d_%d", workerID, i)
						value := fmt.Sprintf("conc_limit_value_%d_%d", workerID, i)
						server.Set(key, value)
					}
				}(w)
			}
			wg.Wait()
			duration := time.Since(start)

			totalOps := concurrency * opsPerWorker
			throughput := float64(totalOps) / duration.Seconds()

			t.Logf("Concurrency: %d, Total ops: %d, Duration: %v, Throughput: %.2f ops/sec",
				concurrency, totalOps, duration, throughput)

			// 验证并发性能合理
			assert.True(t, duration < 10*time.Second, "High concurrency operations should complete within reasonable time")
		})
	}
}
