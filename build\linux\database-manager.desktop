[Desktop Entry]
Version=1.0
Type=Application
Name=Database Manager
Name[zh_CN]=数据库管理器
Comment=Cross-platform database management tool
Comment[zh_CN]=跨平台数据库管理工具
GenericName=Database Manager
GenericName[zh_CN]=数据库管理器
Exec=database-manager %F
Icon=database-manager
Terminal=false
StartupNotify=true
MimeType=text/sql;application/sql;application/json;
Categories=Development;Database;Utility;
Keywords=database;sql;mysql;postgresql;redis;mongodb;
Keywords[zh_CN]=数据库;SQL;MySQL;PostgreSQL;Redis;MongoDB;

# 文件关联
Actions=new-connection;import-config;

[Desktop Action new-connection]
Name=New Database Connection
Name[zh_CN]=新建数据库连接
Exec=database-manager --new-connection

[Desktop Action import-config]
Name=Import Configuration
Name[zh_CN]=导入配置
Exec=database-manager --import-config

# MIME类型关联
[Desktop Entry]
MimeType=text/sql;application/sql;application/x-sql;text/x-sql;

# SQL文件关联
[Desktop Entry]
MimeType=application/json;

# 自定义MIME类型
[Desktop Entry]
MimeType=application/x-database-manager-config;
