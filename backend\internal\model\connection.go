package model

import (
	"time"
)

// DatabaseType 数据库类型枚举
type DatabaseType string

const (
	DatabaseTypeRedis      DatabaseType = "redis"
	DatabaseTypeMySQL      DatabaseType = "mysql"
	DatabaseTypePostgreSQL DatabaseType = "postgresql"
	DatabaseTypeMongoDB    DatabaseType = "mongodb"
)

// ConnectionConfig 数据库连接配置
type ConnectionConfig struct {
	ID        string            `json:"id"`
	Name      string            `json:"name"`
	Type      DatabaseType      `json:"type"`
	Host      string            `json:"host"`
	Port      int               `json:"port"`
	Username  string            `json:"username"`
	Password  string            `json:"password"`
	Database  string            `json:"database"`
	SSL       bool              `json:"ssl"`
	Options   map[string]string `json:"options"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`
}

// ConnectionStatus 连接状态
type ConnectionStatus struct {
	ID        string    `json:"id"`
	Connected bool      `json:"connected"`
	LastPing  time.Time `json:"last_ping"`
	Error     string    `json:"error,omitempty"`
}

// DatabaseInfo 数据库信息
type DatabaseInfo struct {
	Name    string `json:"name"`
	Size    int64  `json:"size"`
	Tables  int    `json:"tables"`
	Version string `json:"version"`
}

// TableInfo 表信息
type TableInfo struct {
	Name    string `json:"name"`
	Type    string `json:"type"`
	Rows    int64  `json:"rows"`
	Size    int64  `json:"size"`
	Comment string `json:"comment"`
}

// ColumnInfo 列信息
type ColumnInfo struct {
	Name            string `json:"name"`
	Type            string `json:"type"`
	Nullable        bool   `json:"nullable"`
	DefaultValue    string `json:"default_value"`
	Comment         string `json:"comment"`
	IsPrimaryKey    bool   `json:"is_primary_key"`
	IsAutoIncrement bool   `json:"is_auto_increment"`
}

// QueryResult 查询结果
type QueryResult struct {
	Columns []string        `json:"columns"`
	Rows    [][]interface{} `json:"rows"`
	Total   int64           `json:"total"`
	Elapsed time.Duration   `json:"elapsed"`
	Error   string          `json:"error,omitempty"`
}

// RedisKeyInfo Redis键信息
type RedisKeyInfo struct {
	Key   string      `json:"key"`
	Type  string      `json:"type"`
	TTL   int64       `json:"ttl"`
	Size  int64       `json:"size"`
	Value interface{} `json:"value,omitempty"`
}

// MongoCollectionInfo MongoDB集合信息
type MongoCollectionInfo struct {
	Name       string `json:"name"`
	Count      int64  `json:"count"`
	Size       int64  `json:"size"`
	AvgObjSize int64  `json:"avg_obj_size"`
	Indexes    int    `json:"indexes"`
}
