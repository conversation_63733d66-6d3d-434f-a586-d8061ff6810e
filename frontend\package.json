{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.3", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^24.0.14", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react": "^2.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^4.6.4", "vite": "^3.0.7"}}