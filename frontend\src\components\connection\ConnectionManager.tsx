import React, { useState, useEffect, useCallback } from 'react';
import { Button, Input, Modal } from '../common';
import ConnectionCard from './ConnectionCard';
import ConnectionForm from './ConnectionForm';
import ConnectionImportExport from './ConnectionImportExport';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  connected: boolean;
  lastConnected?: string;
  connectionCount?: number;
  tags?: string[];
  group?: string;
  favorite?: boolean;
  created_at: string;
  updated_at: string;
}

interface ConnectionGroup {
  name: string;
  connections: Connection[];
  expanded: boolean;
}

interface ConnectionManagerProps {
  onConnectionSelect?: (connection: Connection) => void;
}

const ConnectionManager: React.FC<ConnectionManagerProps> = ({ onConnectionSelect }) => {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [filteredConnections, setFilteredConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedGroup, setSelectedGroup] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'lastConnected' | 'created'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // 模态框状态
  const [showForm, setShowForm] = useState(false);
  const [showImportExport, setShowImportExport] = useState(false);
  const [editingConnection, setEditingConnection] = useState<Connection | null>(null);

  // 连接状态管理
  const [connectingIds, setConnectingIds] = useState<Set<string>>(new Set());
  const [testingIds, setTestingIds] = useState<Set<string>>(new Set());

  // 分组管理
  const [groups, setGroups] = useState<ConnectionGroup[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(['Default']));

  const databaseTypes = [
    { value: 'mysql', label: 'MySQL', icon: '🐬' },
    { value: 'postgresql', label: 'PostgreSQL', icon: '🐘' },
    { value: 'redis', label: 'Redis', icon: '🔴' },
    { value: 'mongodb', label: 'MongoDB', icon: '🍃' },
  ];

  // 获取连接列表
  const fetchConnections = useCallback(async () => {
    try {
      setLoading(true);
      const connectionsData = await (window as any).go.main.App.GetConnections();
      
      const formattedConnections = connectionsData.map((conn: any) => ({
        id: conn.id,
        name: conn.name,
        type: conn.type,
        host: conn.host,
        port: conn.port,
        username: conn.username,
        password: conn.password,
        database: conn.database,
        ssl: conn.ssl,
        connected: false,
        lastConnected: conn.last_connected,
        connectionCount: conn.connection_count || 0,
        tags: conn.tags || [],
        group: conn.group || 'Default',
        favorite: conn.favorite || false,
        created_at: conn.created_at || new Date().toISOString(),
        updated_at: conn.updated_at || new Date().toISOString(),
      }));
      
      setConnections(formattedConnections);
      buildGroups(formattedConnections);
    } catch (error) {
      console.error('Failed to fetch connections:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 构建分组
  const buildGroups = (connections: Connection[]) => {
    const groupMap = new Map<string, Connection[]>();
    
    connections.forEach(conn => {
      const groupName = conn.group || 'Default';
      if (!groupMap.has(groupName)) {
        groupMap.set(groupName, []);
      }
      groupMap.get(groupName)!.push(conn);
    });

    const groupsArray = Array.from(groupMap.entries()).map(([name, connections]) => ({
      name,
      connections: sortConnections(connections),
      expanded: expandedGroups.has(name),
    }));

    setGroups(groupsArray);
  };

  // 排序连接
  const sortConnections = (connections: Connection[]) => {
    return [...connections].sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'lastConnected':
          aValue = new Date(a.lastConnected || 0);
          bValue = new Date(b.lastConnected || 0);
          break;
        case 'created':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  };

  // 过滤连接
  const filterConnections = useCallback(() => {
    let filtered = connections;

    // 按搜索词过滤
    if (searchTerm) {
      filtered = filtered.filter(conn =>
        conn.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conn.host.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conn.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (conn.tags && conn.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
      );
    }

    // 按类型过滤
    if (selectedType !== 'all') {
      filtered = filtered.filter(conn => conn.type === selectedType);
    }

    // 按分组过滤
    if (selectedGroup !== 'all') {
      filtered = filtered.filter(conn => (conn.group || 'Default') === selectedGroup);
    }

    setFilteredConnections(sortConnections(filtered));
  }, [connections, searchTerm, selectedType, selectedGroup, sortBy, sortOrder]);

  // 测试连接
  const testConnection = async (connection: Connection) => {
    const connectionId = connection.id;
    setTestingIds(prev => new Set(prev).add(connectionId));

    try {
      await (window as any).go.main.App.TestConnection({
        name: connection.name,
        type: connection.type,
        host: connection.host,
        port: connection.port,
        username: connection.username,
        password: connection.password,
        database: connection.database,
        ssl: connection.ssl,
      });

      // 更新连接状态
      setConnections(prev => prev.map(conn =>
        conn.id === connectionId
          ? { ...conn, connected: true, lastConnected: new Date().toISOString() }
          : conn
      ));

      return true;
    } catch (error) {
      console.error('Connection test failed:', error);
      setConnections(prev => prev.map(conn =>
        conn.id === connectionId ? { ...conn, connected: false } : conn
      ));
      return false;
    } finally {
      setTestingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(connectionId);
        return newSet;
      });
    }
  };

  // 连接到数据库
  const connectToDatabase = async (connection: Connection) => {
    const success = await testConnection(connection);
    if (success && onConnectionSelect) {
      // 更新连接计数
      setConnections(prev => prev.map(conn =>
        conn.id === connection.id
          ? { ...conn, connectionCount: (conn.connectionCount || 0) + 1 }
          : conn
      ));
      onConnectionSelect(connection);
    }
  };

  // 删除连接
  const deleteConnection = async (connectionId: string) => {
    try {
      await (window as any).go.main.App.DeleteConnection(connectionId);
      setConnections(prev => prev.filter(conn => conn.id !== connectionId));
    } catch (error) {
      console.error('Failed to delete connection:', error);
    }
  };

  // 切换收藏状态
  const toggleFavorite = async (connectionId: string) => {
    const connection = connections.find(c => c.id === connectionId);
    if (!connection) return;

    const updatedConnection = { ...connection, favorite: !connection.favorite };
    
    try {
      await (window as any).go.main.App.SaveConnection(updatedConnection);
      setConnections(prev => prev.map(conn =>
        conn.id === connectionId ? updatedConnection : conn
      ));
    } catch (error) {
      console.error('Failed to update favorite status:', error);
    }
  };

  // 复制连接
  const duplicateConnection = (connection: Connection) => {
    const newConnection = {
      ...connection,
      id: '',
      name: `${connection.name} (Copy)`,
      connected: false,
      lastConnected: undefined,
      connectionCount: 0,
    };
    setEditingConnection(newConnection);
    setShowForm(true);
  };

  // 切换分组展开状态
  const toggleGroup = (groupName: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupName)) {
        newSet.delete(groupName);
      } else {
        newSet.add(groupName);
      }
      return newSet;
    });
  };

  // 处理表单保存
  const handleFormSave = () => {
    setShowForm(false);
    setEditingConnection(null);
    fetchConnections();
  };

  // 处理导入
  const handleImport = (importedConnections: Connection[]) => {
    setConnections(prev => [...prev, ...importedConnections]);
    setShowImportExport(false);
  };

  useEffect(() => {
    fetchConnections();
  }, [fetchConnections]);

  useEffect(() => {
    filterConnections();
  }, [filterConnections]);

  useEffect(() => {
    buildGroups(filteredConnections);
  }, [filteredConnections, expandedGroups]);

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* 顶部工具栏 */}
      <div className="flex-shrink-0 p-6 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Database Connections
          </h1>
          <div className="flex items-center space-x-3">
            <Button
              onClick={() => setShowImportExport(true)}
              variant="outline"
              size="sm"
            >
              Import/Export
            </Button>
            <Button
              onClick={() => {
                setEditingConnection(null);
                setShowForm(true);
              }}
            >
              Add Connection
            </Button>
          </div>
        </div>

        {/* 搜索和过滤 */}
        <div className="flex items-center space-x-4 mb-4">
          <div className="flex-1">
            <Input
              placeholder="Search connections..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Types</option>
            {databaseTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.icon} {type.label}
              </option>
            ))}
          </select>

          <select
            value={selectedGroup}
            onChange={(e) => setSelectedGroup(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Groups</option>
            {Array.from(new Set(connections.map(c => c.group || 'Default'))).map(group => (
              <option key={group} value={group}>{group}</option>
            ))}
          </select>
        </div>

        {/* 视图控制 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">View:</span>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
              >
                Grid
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
              >
                List
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">Sort:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="name">Name</option>
                <option value="type">Type</option>
                <option value="lastConnected">Last Connected</option>
                <option value="created">Created</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400">
            {filteredConnections.length} of {connections.length} connections
          </div>
        </div>
      </div>

      {/* 连接列表 */}
      <div className="flex-1 overflow-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500 dark:text-gray-400">Loading connections...</div>
          </div>
        ) : groups.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
            <div className="text-lg mb-2">No connections found</div>
            <div className="text-sm">Create your first database connection to get started</div>
          </div>
        ) : (
          <div className="space-y-6">
            {groups.map(group => (
              <div key={group.name} className="space-y-3">
                <div
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => toggleGroup(group.name)}
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <span className="mr-2">
                      {group.expanded ? '▼' : '▶'}
                    </span>
                    {group.name}
                    <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                      ({group.connections.length})
                    </span>
                  </h3>
                </div>

                {group.expanded && (
                  <div className={viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                    : 'space-y-2'
                  }>
                    {group.connections.map(connection => (
                      <ConnectionCard
                        key={connection.id}
                        connection={connection}
                        viewMode={viewMode}
                        isConnecting={connectingIds.has(connection.id)}
                        isTesting={testingIds.has(connection.id)}
                        onConnect={() => connectToDatabase(connection)}
                        onTest={() => testConnection(connection)}
                        onEdit={() => {
                          setEditingConnection(connection);
                          setShowForm(true);
                        }}
                        onDelete={() => deleteConnection(connection.id)}
                        onDuplicate={() => duplicateConnection(connection)}
                        onToggleFavorite={() => toggleFavorite(connection.id)}
                      />
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 连接表单模态框 */}
      <Modal
        isOpen={showForm}
        onClose={() => {
          setShowForm(false);
          setEditingConnection(null);
        }}
        title={editingConnection?.id ? 'Edit Connection' : 'Add Connection'}
        size="lg"
      >
        <ConnectionForm
          connection={editingConnection}
          onSave={handleFormSave}
          onCancel={() => {
            setShowForm(false);
            setEditingConnection(null);
          }}
        />
      </Modal>

      {/* 导入导出模态框 */}
      <Modal
        isOpen={showImportExport}
        onClose={() => setShowImportExport(false)}
        title="Import/Export Connections"
        size="lg"
      >
        <ConnectionImportExport
          connections={connections}
          onImport={handleImport}
          onClose={() => setShowImportExport(false)}
        />
      </Modal>
    </div>
  );
};

export default ConnectionManager;
