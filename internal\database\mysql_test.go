package database

import (
	"context"
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMySQLConnector_Connect(t *testing.T) {
	connector := NewMySQLConnector()

	// 测试无效连接配置
	config := ConnectionConfig{
		Host:     "invalid-host",
		Port:     "3306",
		Username: "test",
		Password: "test",
		Database: "test",
	}

	err := connector.Connect(config)
	assert.Error(t, err)
	assert.False(t, connector.IsConnected())
}

func TestMySQLConnector_ExecuteQuery(t *testing.T) {
	// 创建mock数据库
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	connector := &MySQLConnector{
		db:        db,
		connected: true,
	}

	// 测试SELECT查询
	rows := sqlmock.NewRows([]string{"id", "name", "email"}).
		AddRow(1, "<PERSON>", "<EMAIL>").
		AddRow(2, "<PERSON>", "<EMAIL>")

	mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)

	result, err := connector.ExecuteQuery(context.Background(), "SELECT * FROM users")
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(2), result.RowsAffected)

	// 验证结果
	resultRows, ok := result.Rows.([]map[string]interface{})
	assert.True(t, ok)
	assert.Len(t, resultRows, 2)
	assert.Equal(t, int64(1), resultRows[0]["id"])
	assert.Equal(t, "John Doe", resultRows[0]["name"])

	// 确保所有期望都被满足
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMySQLConnector_ExecuteInsert(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	connector := &MySQLConnector{
		db:        db,
		connected: true,
	}

	// 测试INSERT查询
	mock.ExpectExec("INSERT INTO users").
		WithArgs("John Doe", "<EMAIL>").
		WillReturnResult(sqlmock.NewResult(1, 1))

	result, err := connector.ExecuteQuery(context.Background(), "INSERT INTO users (name, email) VALUES (?, ?)", "John Doe", "<EMAIL>")
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.RowsAffected)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMySQLConnector_GetTables(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	connector := &MySQLConnector{
		db:        db,
		connected: true,
	}

	// Mock SHOW TABLES查询
	rows := sqlmock.NewRows([]string{"Tables_in_test"}).
		AddRow("users").
		AddRow("products").
		AddRow("orders")

	mock.ExpectQuery("SHOW TABLES").WillReturnRows(rows)

	tables, err := connector.GetTables(context.Background())
	assert.NoError(t, err)
	assert.Len(t, tables, 3)
	assert.Equal(t, "users", tables[0].Name)
	assert.Equal(t, "products", tables[1].Name)
	assert.Equal(t, "orders", tables[2].Name)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMySQLConnector_GetTableData(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	connector := &MySQLConnector{
		db:        db,
		connected: true,
	}

	// Mock SELECT查询
	rows := sqlmock.NewRows([]string{"id", "name", "email"}).
		AddRow(1, "John Doe", "<EMAIL>").
		AddRow(2, "Jane Smith", "<EMAIL>")

	mock.ExpectQuery("SELECT \\* FROM users LIMIT 100 OFFSET 0").WillReturnRows(rows)

	result, err := connector.GetTableData(context.Background(), "users", 100, 0)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(2), result.RowsAffected)

	resultRows, ok := result.Rows.([]map[string]interface{})
	assert.True(t, ok)
	assert.Len(t, resultRows, 2)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMySQLConnector_TestConnection(t *testing.T) {
	connector := NewMySQLConnector()

	// 测试无效连接
	config := ConnectionConfig{
		Host:     "invalid-host",
		Port:     "3306",
		Username: "test",
		Password: "test",
		Database: "test",
	}

	err := connector.TestConnection(config)
	assert.Error(t, err)
}

func TestMySQLConnector_QueryError(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	connector := &MySQLConnector{
		db:        db,
		connected: true,
	}

	// Mock查询错误
	mock.ExpectQuery("SELECT \\* FROM nonexistent").
		WillReturnError(sql.ErrNoRows)

	result, err := connector.ExecuteQuery(context.Background(), "SELECT * FROM nonexistent")
	assert.Error(t, err)
	assert.Nil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestMySQLConnector_Disconnect(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)

	connector := &MySQLConnector{
		db:        db,
		connected: true,
	}

	assert.True(t, connector.IsConnected())

	err = connector.Disconnect()
	assert.NoError(t, err)
	assert.False(t, connector.IsConnected())
}
