@echo off
echo Building Database Manager...

set APP_NAME=database-manager
set DIST_DIR=dist

if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
mkdir "%DIST_DIR%"

echo Building frontend...
cd frontend
call npm run build
cd ..

echo Building application...
wails build -platform windows/amd64 -nopackage

echo Copying files...
copy "build\bin\%APP_NAME%.exe" "%DIST_DIR%\"

echo Build completed!
echo Executable: %DIST_DIR%\%APP_NAME%.exe

pause
