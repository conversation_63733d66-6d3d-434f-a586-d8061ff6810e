package repository

import (
	"context"
	"database-manager/backend/internal/model"
	"fmt"
	"reflect"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoDBRepository MongoDB连接器实现
type MongoDBRepository struct {
	client    *mongo.Client
	database  *mongo.Database
	config    *model.ConnectionConfig
	connected bool
}

// NewMongoDBRepository 创建MongoDB连接器
func NewMongoDBRepository() *MongoDBRepository {
	return &MongoDBRepository{
		connected: false,
	}
}

// Connect 连接MongoDB数据库
func (m *MongoDBRepository) Connect(ctx context.Context, config *model.ConnectionConfig) error {
	m.config = config

	// 构建连接URI
	var uri string
	if config.Username != "" && config.Password != "" {
		uri = fmt.Sprintf("mongodb://%s:%s@%s:%d/%s",
			config.Username,
			config.Password,
			config.Host,
			config.Port,
			config.Database,
		)
	} else {
		uri = fmt.Sprintf("mongodb://%s:%d/%s",
			config.Host,
			config.Port,
			config.Database,
		)
	}

	// 创建客户端选项
	clientOptions := options.Client().ApplyURI(uri)

	// 添加SSL配置
	if config.SSL {
		clientOptions.SetTLSConfig(nil) // 使用默认TLS配置
	}

	// 添加其他选项
	if config.Options != nil {
		if maxPoolSize, ok := config.Options["maxPoolSize"]; ok {
			if size, err := parseUint64(maxPoolSize); err == nil {
				clientOptions.SetMaxPoolSize(size)
			}
		}

		if connectTimeout, ok := config.Options["connectTimeoutMS"]; ok {
			if timeout, err := parseUint64(connectTimeout); err == nil {
				clientOptions.SetConnectTimeout(time.Duration(timeout) * time.Millisecond)
			}
		}
	}

	// 创建客户端
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	m.client = client
	m.database = client.Database(config.Database)

	// 测试连接
	if err := m.Ping(ctx); err != nil {
		m.client.Disconnect(ctx)
		return fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	m.connected = true
	return nil
}

// parseUint64 解析字符串为uint64
func parseUint64(s string) (uint64, error) {
	var result uint64
	for _, r := range s {
		if r < '0' || r > '9' {
			return 0, fmt.Errorf("invalid number: %s", s)
		}
		result = result*10 + uint64(r-'0')
	}
	return result, nil
}

// Disconnect 断开连接
func (m *MongoDBRepository) Disconnect(ctx context.Context) error {
	if m.client != nil {
		err := m.client.Disconnect(ctx)
		m.client = nil
		m.database = nil
		m.connected = false
		return err
	}
	return nil
}

// Ping 测试连接
func (m *MongoDBRepository) Ping(ctx context.Context) error {
	if m.client == nil {
		return fmt.Errorf("MongoDB client not initialized")
	}

	return m.client.Ping(ctx, nil)
}

// IsConnected 检查连接状态
func (m *MongoDBRepository) IsConnected() bool {
	return m.connected && m.client != nil
}

// GetDatabaseInfo 获取数据库信息
func (m *MongoDBRepository) GetDatabaseInfo(ctx context.Context) (*model.DatabaseInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MongoDB")
	}

	// 获取数据库统计信息
	var stats bson.M
	err := m.database.RunCommand(ctx, bson.D{{Key: "dbStats", Value: 1}}).Decode(&stats)
	if err != nil {
		return nil, fmt.Errorf("failed to get database stats: %w", err)
	}

	// 获取集合数量
	collections, err := m.database.ListCollectionNames(ctx, bson.D{})
	if err != nil {
		collections = []string{}
	}

	// 获取MongoDB版本
	var buildInfo bson.M
	err = m.database.RunCommand(ctx, bson.D{{Key: "buildInfo", Value: 1}}).Decode(&buildInfo)
	version := "unknown"
	if err == nil {
		if v, ok := buildInfo["version"].(string); ok {
			version = v
		}
	}

	// 获取数据库大小
	var size int64
	if dataSize, ok := stats["dataSize"]; ok {
		if s, ok := dataSize.(int64); ok {
			size = s
		} else if s, ok := dataSize.(int32); ok {
			size = int64(s)
		}
	}

	return &model.DatabaseInfo{
		Name:    m.config.Database,
		Size:    size,
		Tables:  len(collections),
		Version: version,
	}, nil
}

// GetTables 获取表列表 (MongoDB中为集合列表)
func (m *MongoDBRepository) GetTables(ctx context.Context, database string) ([]*model.TableInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MongoDB")
	}

	// 获取集合名称列表
	collections, err := m.database.ListCollectionNames(ctx, bson.D{})
	if err != nil {
		return nil, fmt.Errorf("failed to list collections: %w", err)
	}

	var tables []*model.TableInfo
	for _, collName := range collections {
		// 获取集合统计信息
		var stats bson.M
		err := m.database.RunCommand(ctx, bson.D{
			{Key: "collStats", Value: collName},
		}).Decode(&stats)

		table := &model.TableInfo{
			Name: collName,
			Type: "collection",
		}

		if err == nil {
			// 获取文档数量
			if count, ok := stats["count"]; ok {
				if c, ok := count.(int64); ok {
					table.Rows = c
				} else if c, ok := count.(int32); ok {
					table.Rows = int64(c)
				}
			}

			// 获取集合大小
			if size, ok := stats["size"]; ok {
				if s, ok := size.(int64); ok {
					table.Size = s
				} else if s, ok := size.(int32); ok {
					table.Size = int64(s)
				}
			}
		}

		tables = append(tables, table)
	}

	return tables, nil
}

// GetColumns 获取表列信息 (MongoDB中不适用)
func (m *MongoDBRepository) GetColumns(ctx context.Context, database, table string) ([]*model.ColumnInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MongoDB")
	}

	// MongoDB是文档数据库，没有固定的列结构
	// 可以通过采样文档来推断字段结构
	collection := m.database.Collection(table)

	// 获取一个样本文档来推断字段
	var sampleDoc bson.M
	err := collection.FindOne(ctx, bson.D{}).Decode(&sampleDoc)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return []*model.ColumnInfo{}, nil
		}
		return nil, fmt.Errorf("failed to get sample document: %w", err)
	}

	var columns []*model.ColumnInfo
	for key, value := range sampleDoc {
		column := &model.ColumnInfo{
			Name:     key,
			Type:     getMongoFieldType(value),
			Nullable: true, // MongoDB字段默认可为空
		}

		if key == "_id" {
			column.IsPrimaryKey = true
		}

		columns = append(columns, column)
	}

	return columns, nil
}

// getMongoFieldType 获取MongoDB字段类型
func getMongoFieldType(value any) string {
	if value == nil {
		return "null"
	}

	switch reflect.TypeOf(value).Kind() {
	case reflect.String:
		return "string"
	case reflect.Int, reflect.Int32, reflect.Int64:
		return "int"
	case reflect.Float32, reflect.Float64:
		return "double"
	case reflect.Bool:
		return "bool"
	case reflect.Slice:
		return "array"
	case reflect.Map:
		return "object"
	default:
		return "unknown"
	}
}

// ExecuteQuery 执行查询 (MongoDB中执行聚合查询)
func (m *MongoDBRepository) ExecuteQuery(ctx context.Context, query string, params ...any) (*model.QueryResult, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MongoDB")
	}

	start := time.Now()

	// 简单的查询解析 - 实际应用中需要更复杂的解析逻辑
	// 这里假设query是集合名称
	collection := m.database.Collection(query)

	// 执行查找操作
	cursor, err := collection.Find(ctx, bson.D{})
	if err != nil {
		return &model.QueryResult{
			Columns: []string{"Error"},
			Rows:    [][]any{},
			Total:   0,
			Elapsed: time.Since(start),
			Error:   err.Error(),
		}, nil
	}
	defer cursor.Close(ctx)

	// 读取结果
	var documents []bson.M
	if err := cursor.All(ctx, &documents); err != nil {
		return nil, fmt.Errorf("failed to decode documents: %w", err)
	}

	// 如果没有文档，返回空结果
	if len(documents) == 0 {
		return &model.QueryResult{
			Columns: []string{},
			Rows:    [][]any{},
			Total:   0,
			Elapsed: time.Since(start),
		}, nil
	}

	// 从第一个文档获取列名
	var columns []string
	for key := range documents[0] {
		columns = append(columns, key)
	}

	// 转换文档为行数据
	var rows [][]any
	for _, doc := range documents {
		row := make([]any, len(columns))
		for i, col := range columns {
			row[i] = doc[col]
		}
		rows = append(rows, row)
	}

	return &model.QueryResult{
		Columns: columns,
		Rows:    rows,
		Total:   int64(len(rows)),
		Elapsed: time.Since(start),
	}, nil
}

// ExecuteCommand 执行命令
func (m *MongoDBRepository) ExecuteCommand(ctx context.Context, command string, params ...any) error {
	if !m.IsConnected() {
		return fmt.Errorf("not connected to MongoDB")
	}

	// 执行MongoDB命令
	var result bson.M
	err := m.database.RunCommand(ctx, bson.D{{Key: command, Value: 1}}).Decode(&result)
	return err
}

// GetCollections 获取集合列表
func (m *MongoDBRepository) GetCollections(ctx context.Context, database string) ([]*model.MongoCollectionInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MongoDB")
	}

	// 获取集合名称列表
	collections, err := m.database.ListCollectionNames(ctx, bson.D{})
	if err != nil {
		return nil, fmt.Errorf("failed to list collections: %w", err)
	}

	var collInfos []*model.MongoCollectionInfo
	for _, collName := range collections {
		// 获取集合统计信息
		var stats bson.M
		err := m.database.RunCommand(ctx, bson.D{
			{Key: "collStats", Value: collName},
		}).Decode(&stats)

		collInfo := &model.MongoCollectionInfo{
			Name: collName,
		}

		if err == nil {
			// 获取文档数量
			if count, ok := stats["count"]; ok {
				if c, ok := count.(int64); ok {
					collInfo.Count = c
				} else if c, ok := count.(int32); ok {
					collInfo.Count = int64(c)
				}
			}

			// 获取集合大小
			if size, ok := stats["size"]; ok {
				if s, ok := size.(int64); ok {
					collInfo.Size = s
				} else if s, ok := size.(int32); ok {
					collInfo.Size = int64(s)
				}
			}

			// 获取平均对象大小
			if avgObjSize, ok := stats["avgObjSize"]; ok {
				if s, ok := avgObjSize.(int64); ok {
					collInfo.AvgObjSize = s
				} else if s, ok := avgObjSize.(int32); ok {
					collInfo.AvgObjSize = int64(s)
				}
			}

			// 获取索引数量
			if nindexes, ok := stats["nindexes"]; ok {
				if n, ok := nindexes.(int32); ok {
					collInfo.Indexes = int(n)
				} else if n, ok := nindexes.(int64); ok {
					collInfo.Indexes = int(n)
				}
			}
		}

		collInfos = append(collInfos, collInfo)
	}

	return collInfos, nil
}

// FindDocuments 查找文档
func (m *MongoDBRepository) FindDocuments(ctx context.Context, database, collection string, filter map[string]any, limit int64) (*model.QueryResult, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MongoDB")
	}

	start := time.Now()

	coll := m.database.Collection(collection)

	// 构建过滤器
	var bsonFilter bson.D
	for key, value := range filter {
		bsonFilter = append(bsonFilter, bson.E{Key: key, Value: value})
	}

	// 设置查询选项
	opts := options.Find()
	if limit > 0 {
		opts.SetLimit(limit)
	}

	// 执行查询
	cursor, err := coll.Find(ctx, bsonFilter, opts)
	if err != nil {
		return &model.QueryResult{
			Columns: []string{"Error"},
			Rows:    [][]any{},
			Total:   0,
			Elapsed: time.Since(start),
			Error:   err.Error(),
		}, nil
	}
	defer cursor.Close(ctx)

	// 读取结果
	var documents []bson.M
	if err := cursor.All(ctx, &documents); err != nil {
		return nil, fmt.Errorf("failed to decode documents: %w", err)
	}

	// 如果没有文档，返回空结果
	if len(documents) == 0 {
		return &model.QueryResult{
			Columns: []string{},
			Rows:    [][]any{},
			Total:   0,
			Elapsed: time.Since(start),
		}, nil
	}

	// 从第一个文档获取列名
	var columns []string
	for key := range documents[0] {
		columns = append(columns, key)
	}

	// 转换文档为行数据
	var rows [][]any
	for _, doc := range documents {
		row := make([]any, len(columns))
		for i, col := range columns {
			row[i] = doc[col]
		}
		rows = append(rows, row)
	}

	return &model.QueryResult{
		Columns: columns,
		Rows:    rows,
		Total:   int64(len(rows)),
		Elapsed: time.Since(start),
	}, nil
}

// InsertDocument 插入文档
func (m *MongoDBRepository) InsertDocument(ctx context.Context, database, collection string, document map[string]any) error {
	if !m.IsConnected() {
		return fmt.Errorf("not connected to MongoDB")
	}

	coll := m.database.Collection(collection)
	_, err := coll.InsertOne(ctx, document)
	return err
}

// UpdateDocument 更新文档
func (m *MongoDBRepository) UpdateDocument(ctx context.Context, database, collection string, filter, update map[string]any) error {
	if !m.IsConnected() {
		return fmt.Errorf("not connected to MongoDB")
	}

	coll := m.database.Collection(collection)

	// 构建过滤器
	var bsonFilter bson.D
	for key, value := range filter {
		bsonFilter = append(bsonFilter, bson.E{Key: key, Value: value})
	}

	// 构建更新操作
	var bsonUpdate bson.D
	for key, value := range update {
		bsonUpdate = append(bsonUpdate, bson.E{Key: key, Value: value})
	}

	_, err := coll.UpdateMany(ctx, bsonFilter, bson.D{{Key: "$set", Value: bsonUpdate}})
	return err
}

// DeleteDocument 删除文档
func (m *MongoDBRepository) DeleteDocument(ctx context.Context, database, collection string, filter map[string]any) error {
	if !m.IsConnected() {
		return fmt.Errorf("not connected to MongoDB")
	}

	coll := m.database.Collection(collection)

	// 构建过滤器
	var bsonFilter bson.D
	for key, value := range filter {
		bsonFilter = append(bsonFilter, bson.E{Key: key, Value: value})
	}

	_, err := coll.DeleteMany(ctx, bsonFilter)
	return err
}
