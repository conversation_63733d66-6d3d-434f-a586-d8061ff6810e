<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Redis Connection Test</h1>
    
    <div class="test-section">
        <h2>Redis Connection Configuration</h2>
        <form id="redisForm">
            <label>Name:</label>
            <input type="text" id="name" value="Test Redis" required>
            
            <label>Host:</label>
            <input type="text" id="host" value="127.0.0.1" required>
            
            <label>Port:</label>
            <input type="number" id="port" value="6379" required>
            
            <label>Password (optional):</label>
            <input type="password" id="password" value="" placeholder="Leave empty if no password">
            
            <label>Database Number (0-15):</label>
            <input type="number" id="database" value="0" min="0" max="15">
            
            <label>
                <input type="checkbox" id="ssl"> Enable SSL
            </label>
        </form>
        
        <button onclick="testRedisConnection()">Test Redis Connection</button>
        <button onclick="saveRedisConnection()">Save Redis Connection</button>
        
        <div id="testResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Predefined Test Cases</h2>
        <button onclick="testLocalRedis()">Test Local Redis (127.0.0.1:6379)</button>
        <button onclick="testLocalhostRedis()">Test Localhost Redis (localhost:6379)</button>
        <button onclick="testRedisWithAuth()">Test Redis with Password</button>
        
        <div id="predefinedResult"></div>
    </div>

    <script>
        // 检查是否在 Wails 环境中
        function isWailsEnvironment() {
            return window.go && window.go.main && window.go.main.App;
        }

        // 显示结果
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        // 获取表单数据
        function getRedisFormData() {
            return {
                name: document.getElementById('name').value,
                type: 'redis',
                host: document.getElementById('host').value,
                port: parseInt(document.getElementById('port').value),
                username: '', // Redis 通常不需要用户名
                password: document.getElementById('password').value,
                database: document.getElementById('database').value,
                ssl: document.getElementById('ssl').checked,
                options: {}
            };
        }

        // 测试 Redis 连接
        async function testRedisConnection() {
            if (!isWailsEnvironment()) {
                showResult('testResult', 'Error: Not running in Wails environment', false);
                return;
            }

            const formData = getRedisFormData();
            
            try {
                console.log('Testing Redis connection with data:', formData);
                await window.go.main.App.TestConnection(formData);
                showResult('testResult', `✅ Redis connection test successful! Connected to ${formData.host}:${formData.port}`, true);
            } catch (error) {
                console.error('Redis connection test failed:', error);
                showResult('testResult', `❌ Redis connection test failed: ${error.message}`, false);
            }
        }

        // 保存 Redis 连接
        async function saveRedisConnection() {
            if (!isWailsEnvironment()) {
                showResult('testResult', 'Error: Not running in Wails environment', false);
                return;
            }

            const formData = getRedisFormData();
            
            try {
                await window.go.main.App.SaveConnection(formData);
                showResult('testResult', '✅ Redis connection saved successfully!', true);
            } catch (error) {
                showResult('testResult', `❌ Failed to save Redis connection: ${error.message}`, false);
            }
        }

        // 测试本地 Redis (127.0.0.1)
        async function testLocalRedis() {
            if (!isWailsEnvironment()) {
                showResult('predefinedResult', 'Error: Not running in Wails environment', false);
                return;
            }

            const testData = {
                name: 'Local Redis 127.0.0.1',
                type: 'redis',
                host: '127.0.0.1',
                port: 6379,
                username: '',
                password: '',
                database: '0',
                ssl: false,
                options: {}
            };
            
            try {
                console.log('Testing local Redis (127.0.0.1) with data:', testData);
                await window.go.main.App.TestConnection(testData);
                showResult('predefinedResult', '✅ Local Redis (127.0.0.1:6379) connection successful!', true);
            } catch (error) {
                console.error('Local Redis test failed:', error);
                showResult('predefinedResult', `❌ Local Redis (127.0.0.1:6379) test failed: ${error.message}`, false);
            }
        }

        // 测试本地 Redis (localhost)
        async function testLocalhostRedis() {
            if (!isWailsEnvironment()) {
                showResult('predefinedResult', 'Error: Not running in Wails environment', false);
                return;
            }

            const testData = {
                name: 'Local Redis localhost',
                type: 'redis',
                host: 'localhost',
                port: 6379,
                username: '',
                password: '',
                database: '0',
                ssl: false,
                options: {}
            };
            
            try {
                console.log('Testing localhost Redis with data:', testData);
                await window.go.main.App.TestConnection(testData);
                showResult('predefinedResult', '✅ Localhost Redis (localhost:6379) connection successful!', true);
            } catch (error) {
                console.error('Localhost Redis test failed:', error);
                showResult('predefinedResult', `❌ Localhost Redis (localhost:6379) test failed: ${error.message}`, false);
            }
        }

        // 测试带密码的 Redis
        async function testRedisWithAuth() {
            if (!isWailsEnvironment()) {
                showResult('predefinedResult', 'Error: Not running in Wails environment', false);
                return;
            }

            const testData = {
                name: 'Redis with Auth',
                type: 'redis',
                host: '127.0.0.1',
                port: 6379,
                username: '',
                password: 'testpassword',
                database: '0',
                ssl: false,
                options: {}
            };
            
            try {
                console.log('Testing Redis with auth:', testData);
                await window.go.main.App.TestConnection(testData);
                showResult('predefinedResult', '✅ Redis with password connection successful!', true);
            } catch (error) {
                console.error('Redis with auth test failed:', error);
                showResult('predefinedResult', `❌ Redis with password test failed: ${error.message}`, false);
            }
        }

        // 页面加载时检查环境
        window.onload = function() {
            if (isWailsEnvironment()) {
                showResult('testResult', '✅ Wails environment detected. Ready to test Redis connections!', true);
            } else {
                showResult('testResult', '⚠️ Warning: Not running in Wails environment. Please run this in the Database Manager app.', false);
            }
        };
    </script>
</body>
</html>
