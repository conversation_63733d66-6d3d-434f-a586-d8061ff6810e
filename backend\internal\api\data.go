package api

import (
	"context"
	"database-manager/backend/internal/repository"
	"encoding/json"
	"fmt"
	"net/http"
)

// DataAPI 数据操作API
type DataAPI struct {
	connectionManager *repository.ConnectionManager
}

// NewDataAPI 创建数据操作API
func NewDataAPI(connectionManager *repository.ConnectionManager) *DataAPI {
	return &DataAPI{
		connectionManager: connectionManager,
	}
}

// ExecuteCommandRequest 执行命令请求
type ExecuteCommandRequest struct {
	ConnectionID string   `json:"connection_id"`
	Command      string   `json:"command"`
	Params       []string `json:"params"`
}

// TransactionRequest 事务请求
type TransactionRequest struct {
	ConnectionID string `json:"connection_id"`
	Action       string `json:"action"` // begin, commit, rollback
}

// RedisSetRequest Redis设置值请求
type RedisSetRequest struct {
	ConnectionID string `json:"connection_id"`
	Key          string `json:"key"`
	Value        any    `json:"value"`
	TTL          int64  `json:"ttl"`
}

// RedisDeleteRequest Redis删除键请求
type RedisDeleteRequest struct {
	ConnectionID string `json:"connection_id"`
	Key          string `json:"key"`
}

// MongoInsertRequest MongoDB插入文档请求
type MongoInsertRequest struct {
	ConnectionID string         `json:"connection_id"`
	Database     string         `json:"database"`
	Collection   string         `json:"collection"`
	Document     map[string]any `json:"document"`
}

// MongoUpdateRequest MongoDB更新文档请求
type MongoUpdateRequest struct {
	ConnectionID string         `json:"connection_id"`
	Database     string         `json:"database"`
	Collection   string         `json:"collection"`
	Filter       map[string]any `json:"filter"`
	Update       map[string]any `json:"update"`
}

// MongoDeleteRequest MongoDB删除文档请求
type MongoDeleteRequest struct {
	ConnectionID string         `json:"connection_id"`
	Database     string         `json:"database"`
	Collection   string         `json:"collection"`
	Filter       map[string]any `json:"filter"`
}

// ExecuteCommand 执行命令
func (api *DataAPI) ExecuteCommand(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req ExecuteCommandRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Command == "" {
		api.writeError(w, http.StatusBadRequest, "Command is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 转换参数
	params := make([]any, len(req.Params))
	for i, param := range req.Params {
		params[i] = param
	}

	// 执行命令
	if err := conn.ExecuteCommand(context.Background(), req.Command, params...); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Command execution failed: %v", err))
		return
	}

	api.writeSuccess(w, nil, "Command executed successfully")
}

// ManageTransaction 管理事务
func (api *DataAPI) ManageTransaction(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req TransactionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Action == "" {
		api.writeError(w, http.StatusBadRequest, "Action is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否支持事务
	sqlConn, ok := conn.(repository.SQLConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Database does not support transactions")
		return
	}

	// 执行事务操作
	switch req.Action {
	case "begin":
		err = sqlConn.BeginTransaction(context.Background())
	case "commit":
		err = sqlConn.CommitTransaction(context.Background())
	case "rollback":
		err = sqlConn.RollbackTransaction(context.Background())
	default:
		api.writeError(w, http.StatusBadRequest, "Invalid transaction action")
		return
	}

	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Transaction %s failed: %v", req.Action, err))
		return
	}

	api.writeSuccess(w, nil, fmt.Sprintf("Transaction %s successful", req.Action))
}

// SetRedisValue 设置Redis值
func (api *DataAPI) SetRedisValue(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req RedisSetRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Key == "" {
		api.writeError(w, http.StatusBadRequest, "Key is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为Redis连接
	redisConn, ok := conn.(repository.RedisConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a Redis database")
		return
	}

	// 设置值
	if err := redisConn.SetValue(context.Background(), req.Key, req.Value, req.TTL); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to set Redis value: %v", err))
		return
	}

	api.writeSuccess(w, nil, "Redis value set successfully")
}

// DeleteRedisKey 删除Redis键
func (api *DataAPI) DeleteRedisKey(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req RedisDeleteRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Key == "" {
		api.writeError(w, http.StatusBadRequest, "Key is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为Redis连接
	redisConn, ok := conn.(repository.RedisConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a Redis database")
		return
	}

	// 删除键
	if err := redisConn.DeleteKey(context.Background(), req.Key); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to delete Redis key: %v", err))
		return
	}

	api.writeSuccess(w, nil, "Redis key deleted successfully")
}

// InsertMongoDocument 插入MongoDB文档
func (api *DataAPI) InsertMongoDocument(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req MongoInsertRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Database == "" {
		api.writeError(w, http.StatusBadRequest, "Database is required")
		return
	}
	if req.Collection == "" {
		api.writeError(w, http.StatusBadRequest, "Collection is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为MongoDB连接
	mongoConn, ok := conn.(repository.MongoConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a MongoDB database")
		return
	}

	// 插入文档
	if err := mongoConn.InsertDocument(context.Background(), req.Database, req.Collection, req.Document); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to insert MongoDB document: %v", err))
		return
	}

	api.writeSuccess(w, nil, "MongoDB document inserted successfully")
}

// UpdateMongoDocument 更新MongoDB文档
func (api *DataAPI) UpdateMongoDocument(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req MongoUpdateRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Database == "" {
		api.writeError(w, http.StatusBadRequest, "Database is required")
		return
	}
	if req.Collection == "" {
		api.writeError(w, http.StatusBadRequest, "Collection is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为MongoDB连接
	mongoConn, ok := conn.(repository.MongoConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a MongoDB database")
		return
	}

	// 更新文档
	if err := mongoConn.UpdateDocument(context.Background(), req.Database, req.Collection, req.Filter, req.Update); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to update MongoDB document: %v", err))
		return
	}

	api.writeSuccess(w, nil, "MongoDB document updated successfully")
}

// DeleteMongoDocument 删除MongoDB文档
func (api *DataAPI) DeleteMongoDocument(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req MongoDeleteRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Database == "" {
		api.writeError(w, http.StatusBadRequest, "Database is required")
		return
	}
	if req.Collection == "" {
		api.writeError(w, http.StatusBadRequest, "Collection is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为MongoDB连接
	mongoConn, ok := conn.(repository.MongoConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a MongoDB database")
		return
	}

	// 删除文档
	if err := mongoConn.DeleteDocument(context.Background(), req.Database, req.Collection, req.Filter); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to delete MongoDB document: %v", err))
		return
	}

	api.writeSuccess(w, nil, "MongoDB document deleted successfully")
}

// 辅助方法

// writeSuccess 写入成功响应
func (api *DataAPI) writeSuccess(w http.ResponseWriter, data interface{}, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := APIResponse{
		Success: true,
		Data:    data,
		Message: message,
	}

	json.NewEncoder(w).Encode(response)
}

// writeError 写入错误响应
func (api *DataAPI) writeError(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := APIResponse{
		Success: false,
		Error:   message,
	}

	json.NewEncoder(w).Encode(response)
}
