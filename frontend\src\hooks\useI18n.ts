import { useState, useEffect } from 'react';
import enLocale from '../i18n/locales/en.json';
import zhLocale from '../i18n/locales/zh.json';

export type Language = 'en' | 'zh';

interface I18nConfig {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const LANGUAGE_STORAGE_KEY = 'database-manager-language';

// 语言资源
const locales = {
  en: enLocale,
  zh: zhLocale,
};

// 获取浏览器语言
const getBrowserLanguage = (): Language => {
  const browserLang = navigator.language.toLowerCase();
  if (browserLang.startsWith('zh')) {
    return 'zh';
  }
  return 'en';
};

export const useI18n = (): I18nConfig => {
  const [language, setLanguageState] = useState<Language>(() => {
    // 从localStorage获取保存的语言设置
    const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY) as Language;
    return savedLanguage || getBrowserLanguage();
  });

  // 设置语言
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    localStorage.setItem(LANGUAGE_STORAGE_KEY, newLanguage);
  };

  // 翻译函数
  const t = (key: string, params?: Record<string, string | number>): string => {
    const keys = key.split('.');
    let value: any = locales[language];

    // 遍历键路径获取翻译值
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // 如果找不到翻译，返回键名
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }

    // 如果最终值不是字符串，返回键名
    if (typeof value !== 'string') {
      console.warn(`Translation value is not a string: ${key}`);
      return key;
    }

    // 替换参数
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match;
      });
    }

    return value;
  };

  return {
    language,
    setLanguage,
    t,
  };
};