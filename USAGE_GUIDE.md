# Database Manager 使用指南

## 快速开始

### 1. 构建和运行应用

```bash
# 1. 安装前端依赖
cd frontend
npm install

# 2. 构建前端
npm run build

# 3. 返回根目录并构建应用
cd ..
go build

# 4. 运行应用
./database-manager.exe
```

### 2. 添加数据库连接

1. 点击 **"Add Connection"** 按钮
2. 填写连接信息：
   - **Connection Name**: 连接的显示名称
   - **Database Type**: 选择数据库类型（MySQL, PostgreSQL, Redis, MongoDB）
   - **Host**: 数据库服务器地址
   - **Port**: 数据库端口号
   - **Username**: 用户名
   - **Password**: 密码
   - **Database**: 数据库名称（Redis 除外）
   - **Enable SSL**: 是否启用 SSL 连接

3. 点击 **"Test Connection"** 验证连接
4. 点击 **"Save"** 保存连接配置

### 3. 管理连接

- **测试连接**: 点击连接列表中的 "Test" 按钮
- **编辑连接**: 点击 "Edit" 按钮修改连接配置
- **删除连接**: 点击 "Delete" 按钮删除连接

## 支持的数据库类型

### MySQL
- 默认端口: 3306
- 需要提供: host, port, username, password, database

### PostgreSQL  
- 默认端口: 5432
- 需要提供: host, port, username, password, database

### Redis
- 默认端口: 6379
- 需要提供: host, port, password (可选)
- Database 字段用于指定 Redis 数据库编号 (0-15)

### MongoDB
- 默认端口: 27017
- 需要提供: host, port, username (可选), password (可选), database

## 故障排除

### 连接测试失败
1. 检查数据库服务是否运行
2. 验证主机地址和端口号
3. 确认用户名和密码正确
4. 检查防火墙设置
5. 对于 SSL 连接，确保证书配置正确

### 应用无法启动
1. 确保已安装 Go 1.23+ 和 Node.js
2. 检查前端是否已构建 (`frontend/dist` 目录存在)
3. 运行 `go mod tidy` 确保依赖完整

### 保存连接失败
1. 检查应用是否有写入权限
2. 确保所有必填字段都已填写
3. 查看控制台错误信息

## 开发模式

如果需要在开发模式下运行：

```bash
# 使用 Wails 开发模式（如果图标问题已解决）
wails dev

# 或者手动构建和运行
npm run build --prefix frontend
go build
./database-manager.exe
```

## 测试页面

项目包含一个测试页面 `test_connection_fix.html`，可以在浏览器中打开来测试连接功能（需要在 Wails 应用中运行）。

## 技术支持

如果遇到问题：
1. 查看 `CONNECTION_FIX_SUMMARY.md` 了解修复详情
2. 检查控制台错误信息
3. 确保使用的是最新版本的代码
