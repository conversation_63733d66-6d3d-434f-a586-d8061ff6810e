Windows Registry Editor Version 5.00

; Database Manager 文件关联配置
; 支持的文件类型: .sql, .dbm (Database Manager配置文件)

; 注册 .sql 文件关联
[HKEY_CLASSES_ROOT\.sql]
@="DatabaseManager.SQLFile"
"Content Type"="text/sql"

[HKEY_CLASSES_ROOT\DatabaseManager.SQLFile]
@="SQL Script File"
"EditFlags"=dword:00000000

[HKEY_CLASSES_ROOT\DatabaseManager.SQLFile\DefaultIcon]
@="%ProgramFiles%\\Database Manager\\database-manager.exe,1"

[HKEY_CLASSES_ROOT\DatabaseManager.SQLFile\shell]
@="open"

[HKEY_CLASSES_ROOT\DatabaseManager.SQLFile\shell\open]
@="使用 Database Manager 打开"

[HKEY_CLASSES_ROOT\DatabaseManager.SQLFile\shell\open\command]
@="\"%ProgramFiles%\\Database Manager\\database-manager.exe\" \"%1\""

[HKEY_CLASSES_ROOT\DatabaseManager.SQLFile\shell\edit]
@="编辑 SQL 文件"

[HKEY_CLASSES_ROOT\DatabaseManager.SQLFile\shell\edit\command]
@="\"%ProgramFiles%\\Database Manager\\database-manager.exe\" --edit \"%1\""

; 注册 .dbm 文件关联 (Database Manager 配置文件)
[HKEY_CLASSES_ROOT\.dbm]
@="DatabaseManager.ConfigFile"
"Content Type"="application/json"

[HKEY_CLASSES_ROOT\DatabaseManager.ConfigFile]
@="Database Manager Configuration File"
"EditFlags"=dword:00000000

[HKEY_CLASSES_ROOT\DatabaseManager.ConfigFile\DefaultIcon]
@="%ProgramFiles%\\Database Manager\\database-manager.exe,2"

[HKEY_CLASSES_ROOT\DatabaseManager.ConfigFile\shell]
@="open"

[HKEY_CLASSES_ROOT\DatabaseManager.ConfigFile\shell\open]
@="使用 Database Manager 打开"

[HKEY_CLASSES_ROOT\DatabaseManager.ConfigFile\shell\open\command]
@="\"%ProgramFiles%\\Database Manager\\database-manager.exe\" --config \"%1\""

; 右键菜单集成
[HKEY_CLASSES_ROOT\Directory\shell\DatabaseManager]
@="在此处打开 Database Manager"
"Icon"="%ProgramFiles%\\Database Manager\\database-manager.exe,0"

[HKEY_CLASSES_ROOT\Directory\shell\DatabaseManager\command]
@="\"%ProgramFiles%\\Database Manager\\database-manager.exe\" --directory \"%1\""

[HKEY_CLASSES_ROOT\Directory\Background\shell\DatabaseManager]
@="在此处打开 Database Manager"
"Icon"="%ProgramFiles%\\Database Manager\\database-manager.exe,0"

[HKEY_CLASSES_ROOT\Directory\Background\shell\DatabaseManager\command]
@="\"%ProgramFiles%\\Database Manager\\database-manager.exe\" --directory \"%V\""

; 添加到"发送到"菜单
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\SendTo\Database Manager.lnk]

; 程序信息注册
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\database-manager.exe]
@="%ProgramFiles%\\Database Manager\\database-manager.exe"
"Path"="%ProgramFiles%\\Database Manager"

; 卸载信息
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\DatabaseManager]
"DisplayName"="Database Manager"
"DisplayVersion"="1.0.0"
"Publisher"="Database Manager Team"
"InstallLocation"="%ProgramFiles%\\Database Manager"
"UninstallString"="%ProgramFiles%\\Database Manager\\uninstall.exe"
"DisplayIcon"="%ProgramFiles%\\Database Manager\\database-manager.exe,0"
"EstimatedSize"=dword:00002710
"NoModify"=dword:00000001
"NoRepair"=dword:00000001
