name: Build and Release

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  GO_VERSION: '1.21'
  NODE_VERSION: '18'
  WAILS_VERSION: 'latest'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Install Go dependencies
      run: go mod download
    
    - name: Run Go tests
      run: go test -v ./...
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm test
    
    - name: Build frontend
      run: |
        cd frontend
        npm run build

  build:
    name: Build
    needs: test
    strategy:
      matrix:
        platform:
          - os: ubuntu-latest
            target: linux/amd64
            name: linux
          - os: windows-latest
            target: windows/amd64
            name: windows
          - os: macos-latest
            target: darwin/amd64
            name: macos
    runs-on: ${{ matrix.platform.os }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install Wails
      run: go install github.com/wailsapp/wails/v2/cmd/wails@${{ env.WAILS_VERSION }}
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Install Go dependencies
      run: go mod download
    
    - name: Build application
      run: wails build -platform ${{ matrix.platform.target }} -clean
    
    - name: Package for Linux
      if: matrix.platform.name == 'linux'
      run: |
        chmod +x build/linux/build.sh
        ./build/linux/build.sh
    
    - name: Package for Windows
      if: matrix.platform.name == 'windows'
      run: |
        build/windows/build.bat
    
    - name: Package for macOS
      if: matrix.platform.name == 'macos'
      run: |
        chmod +x build/macos/build.sh
        ./build/macos/build.sh
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: database-manager-${{ matrix.platform.name }}
        path: build/${{ matrix.platform.name }}/dist/
        retention-days: 30

  release:
    name: Release
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts/
    
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Database Manager ${{ github.ref }}
        draft: false
        prerelease: false
        body: |
          ## Database Manager Release
          
          ### Features
          - Cross-platform database management tool
          - Support for MySQL, PostgreSQL, Redis, MongoDB
          - Modern web-based user interface
          - Dark/Light theme support
          - Multi-language support (English/Chinese)
          
          ### Downloads
          - **Windows**: database-manager-setup.exe (Installer) or database-manager-portable-windows-amd64.zip (Portable)
          - **macOS**: Database Manager-1.0.0-macOS.dmg (Installer) or Database Manager-portable-macOS.zip (Portable)
          - **Linux**: database-manager_1.0.0_amd64.deb (DEB) or Database Manager-1.0.0-x86_64.AppImage (AppImage)
          
          ### Installation
          1. Download the appropriate package for your platform
          2. Install or extract the package
          3. Run Database Manager
          
          ### System Requirements
          - **Windows**: Windows 10 or later
          - **macOS**: macOS 10.13 or later
          - **Linux**: Ubuntu 18.04+ or equivalent
    
    - name: Upload Windows Installer
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: artifacts/database-manager-windows/database-manager-setup.exe
        asset_name: database-manager-setup.exe
        asset_content_type: application/octet-stream
    
    - name: Upload Windows Portable
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: artifacts/database-manager-windows/database-manager-portable-windows-amd64.zip
        asset_name: database-manager-portable-windows-amd64.zip
        asset_content_type: application/zip
    
    - name: Upload macOS DMG
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: artifacts/database-manager-macos/Database Manager-1.0.0-macOS.dmg
        asset_name: Database Manager-1.0.0-macOS.dmg
        asset_content_type: application/octet-stream
    
    - name: Upload macOS Portable
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: artifacts/database-manager-macos/Database Manager-portable-macOS.zip
        asset_name: Database Manager-portable-macOS.zip
        asset_content_type: application/zip
    
    - name: Upload Linux DEB
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: artifacts/database-manager-linux/database-manager_1.0.0_amd64.deb
        asset_name: database-manager_1.0.0_amd64.deb
        asset_content_type: application/octet-stream
    
    - name: Upload Linux AppImage
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: artifacts/database-manager-linux/Database Manager-1.0.0-x86_64.AppImage
        asset_name: Database Manager-1.0.0-x86_64.AppImage
        asset_content_type: application/octet-stream
