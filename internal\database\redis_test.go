package database

import (
	"context"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRedisConnector_Connect(t *testing.T) {
	// 启动内存中的Redis服务器用于测试
	s, err := miniredis.Run()
	require.NoError(t, err)
	defer s.Close()

	connector := NewRedisConnector()
	config := ConnectionConfig{
		Host:     s.Host(),
		Port:     s.Port(),
		Database: "0",
	}

	err = connector.Connect(config)
	assert.NoError(t, err)
	assert.True(t, connector.IsConnected())

	err = connector.Disconnect()
	assert.NoError(t, err)
	assert.False(t, connector.IsConnected())
}

func TestRedisConnector_ConnectWithAuth(t *testing.T) {
	s, err := miniredis.Run()
	require.NoError(t, err)
	defer s.Close()

	// 设置密码
	s.RequireAuth("testpassword")

	connector := NewRedisConnector()
	config := ConnectionConfig{
		Host:     s.Host(),
		Port:     s.Port(),
		Password: "testpassword",
		Database: "0",
	}

	err = connector.Connect(config)
	assert.NoError(t, err)
	assert.True(t, connector.IsConnected())

	err = connector.Disconnect()
	assert.NoError(t, err)
}

func TestRedisConnector_ConnectWithWrongAuth(t *testing.T) {
	s, err := miniredis.Run()
	require.NoError(t, err)
	defer s.Close()

	s.RequireAuth("correctpassword")

	connector := NewRedisConnector()
	config := ConnectionConfig{
		Host:     s.Host(),
		Port:     s.Port(),
		Password: "wrongpassword",
		Database: "0",
	}

	err = connector.Connect(config)
	assert.Error(t, err)
	assert.False(t, connector.IsConnected())
}

func TestRedisConnector_ExecuteQuery(t *testing.T) {
	s, err := miniredis.Run()
	require.NoError(t, err)
	defer s.Close()

	connector := NewRedisConnector()
	config := ConnectionConfig{
		Host:     s.Host(),
		Port:     s.Port(),
		Database: "0",
	}

	err = connector.Connect(config)
	require.NoError(t, err)
	defer connector.Disconnect()

	// 测试SET命令
	result, err := connector.ExecuteQuery(context.Background(), "SET test_key test_value")
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 测试GET命令
	result, err = connector.ExecuteQuery(context.Background(), "GET test_key")
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证结果
	rows, ok := result.Rows.([]map[string]interface{})
	assert.True(t, ok)
	assert.Len(t, rows, 1)
	assert.Equal(t, "test_value", rows[0]["value"])
}

func TestRedisConnector_GetTables(t *testing.T) {
	s, err := miniredis.Run()
	require.NoError(t, err)
	defer s.Close()

	connector := NewRedisConnector()
	config := ConnectionConfig{
		Host:     s.Host(),
		Port:     s.Port(),
		Database: "0",
	}

	err = connector.Connect(config)
	require.NoError(t, err)
	defer connector.Disconnect()

	// 添加一些测试数据
	s.Set("key1", "value1")
	s.Set("key2", "value2")
	s.HSet("hash1", "field1", "value1")

	tables, err := connector.GetTables(context.Background())
	assert.NoError(t, err)
	assert.NotEmpty(t, tables)

	// Redis应该返回键类型作为"表"
	tableNames := make([]string, len(tables))
	for i, table := range tables {
		tableNames[i] = table.Name
	}
	assert.Contains(t, tableNames, "Keys")
}

func TestRedisConnector_TestConnection(t *testing.T) {
	s, err := miniredis.Run()
	require.NoError(t, err)
	defer s.Close()

	connector := NewRedisConnector()
	config := ConnectionConfig{
		Host:     s.Host(),
		Port:     s.Port(),
		Database: "0",
	}

	// 测试有效连接
	err = connector.TestConnection(config)
	assert.NoError(t, err)

	// 测试无效连接
	invalidConfig := ConnectionConfig{
		Host:     "invalid-host",
		Port:     "6379",
		Database: "0",
	}
	err = connector.TestConnection(invalidConfig)
	assert.Error(t, err)
}

func TestRedisConnector_ConnectionTimeout(t *testing.T) {
	connector := NewRedisConnector()
	config := ConnectionConfig{
		Host:     "*********", // 不可达的IP地址
		Port:     "6379",
		Database: "0",
	}

	// 设置较短的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	start := time.Now()
	err := connector.TestConnection(config)
	duration := time.Since(start)

	assert.Error(t, err)
	assert.True(t, duration < 2*time.Second, "Connection should timeout quickly")
}
