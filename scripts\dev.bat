@echo off
echo Starting Database Manager in development mode...

REM 检查Wails是否安装
wails version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Wails is not installed. Please install Wails first.
    echo Run: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed. Please install Node.js first.
    exit /b 1
)

echo Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies
    exit /b 1
)

cd ..

echo Starting development server...
wails dev