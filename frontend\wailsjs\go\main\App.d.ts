// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {main} from '../models';

export function CenterWindow():Promise<void>;

export function CheckForUpdates():Promise<Record<string, any>>;

export function DeleteConnection(arg1:string):Promise<void>;

export function ExecuteQuery(arg1:string,arg2:string):Promise<main.QueryResult>;

export function ForceGarbageCollection():Promise<void>;

export function GetBuildInfo():Promise<Record<string, string>>;

export function GetConnections():Promise<Array<main.Connection>>;

export function GetFullVersionString():Promise<string>;

export function GetMemoryStats():Promise<Record<string, any>>;

export function GetSupportedFileTypes():Promise<Array<main.FileAssociation>>;

export function GetSystemTraySettings():Promise<main.SystemTraySettings>;

export function GetTrayMenuItems():Promise<Array<Record<string, any>>>;

export function GetVersion():Promise<Record<string, any>>;

export function GetVersionString():Promise<string>;

export function GetWindowSettings():Promise<main.WindowSettings>;

export function GetWindowState():Promise<Record<string, any>>;

export function HandleCommandLineArgs(arg1:Array<string>):Promise<void>;

export function HandleTrayMenuClick(arg1:string):Promise<void>;

export function HideWindow():Promise<void>;

export function IsDevVersion():Promise<boolean>;

export function MaximizeWindow():Promise<void>;

export function MinimizeToTray():Promise<void>;

export function MinimizeWindow():Promise<void>;

export function OpenFile(arg1:string):Promise<void>;

export function OptimizeMemory():Promise<void>;

export function QuitApplication():Promise<void>;

export function RegisterFileAssociations():Promise<void>;

export function RestoreFromTray():Promise<void>;

export function RestoreWindow():Promise<void>;

export function SaveConnection(arg1:Record<string, any>):Promise<void>;

export function SetGCInterval(arg1:number):Promise<void>;

export function SetMemoryThreshold(arg1:number):Promise<void>;

export function SetSystemTraySettings(arg1:main.SystemTraySettings):Promise<void>;

export function SetWindowAlwaysOnTop(arg1:boolean):Promise<void>;

export function SetWindowPosition(arg1:number,arg2:number):Promise<void>;

export function SetWindowSize(arg1:number,arg2:number):Promise<void>;

export function SetWindowTitle(arg1:string):Promise<void>;

export function ShowNotification(arg1:string,arg2:string):Promise<void>;

export function ShowWindow():Promise<void>;

export function TestConnection(arg1:Record<string, any>):Promise<void>;

export function ToggleMaximize():Promise<void>;

export function UnregisterFileAssociations():Promise<void>;
