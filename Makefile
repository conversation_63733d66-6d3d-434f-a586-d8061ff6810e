# Database Manager Makefile

.PHONY: help install dev build build-all clean test lint format

# Default target
help:
	@echo "Database Manager Build Commands:"
	@echo "  install     - Install dependencies"
	@echo "  dev         - Start development server"
	@echo "  build       - Build for current platform"
	@echo "  build-all   - Build for all platforms"
	@echo "  clean       - Clean build artifacts"
	@echo "  test        - Run tests"
	@echo "  lint        - Run linters"
	@echo "  format      - Format code"

# Install dependencies
install:
	@echo "Installing Go dependencies..."
	go mod download
	@echo "Installing frontend dependencies..."
	cd frontend && npm install

# Start development server
dev:
	@echo "Starting development server..."
	wails dev

# Build for current platform
build:
	@echo "Building for current platform..."
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Building frontend..."
	cd frontend && npm run build
	@echo "Building Wails application..."
	wails build

# Build for all platforms
build-all:
	@echo "Building for all platforms..."
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Building frontend..."
	cd frontend && npm run build
	@echo "Building for Windows (amd64)..."
	wails build -platform windows/amd64 -o database-manager-windows-amd64.exe
	@echo "Building for macOS (amd64)..."
	wails build -platform darwin/amd64 -o database-manager-macos-amd64
	@echo "Building for macOS (arm64)..."
	wails build -platform darwin/arm64 -o database-manager-macos-arm64
	@echo "Building for Linux (amd64)..."
	wails build -platform linux/amd64 -o database-manager-linux-amd64

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf build/bin
	rm -rf frontend/dist
	rm -rf frontend/node_modules

# Run tests
test:
	@echo "Running Go tests..."
	go test ./...
	@echo "Running frontend tests..."
	cd frontend && npm test

# Run linters
lint:
	@echo "Running Go linter..."
	golangci-lint run
	@echo "Running frontend linter..."
	cd frontend && npm run lint

# Format code
format:
	@echo "Formatting Go code..."
	go fmt ./...
	@echo "Formatting frontend code..."
	cd frontend && npm run format

# Check if tools are installed
check-tools:
	@echo "Checking required tools..."
	@command -v go >/dev/null 2>&1 || { echo "Go is not installed"; exit 1; }
	@command -v node >/dev/null 2>&1 || { echo "Node.js is not installed"; exit 1; }
	@command -v npm >/dev/null 2>&1 || { echo "npm is not installed"; exit 1; }
	@command -v wails >/dev/null 2>&1 || { echo "Wails is not installed"; exit 1; }
	@echo "All required tools are installed!"