{"$schema": "https://wails.io/schemas/config.v2.json", "name": "database-manager", "outputfilename": "database-manager", "frontend": {"dir": "./frontend", "install": "npm install", "build": "npm run build", "dev": "npm run dev", "package": {"manager": "npm"}}, "author": {"name": "Database Manager Team", "email": "<EMAIL>"}, "info": {"companyName": "Database Manager", "productName": "Database Manager", "productVersion": "1.0.0", "copyright": "Copyright © 2024 Database Manager Team", "comments": "A modern database management tool"}, "nsisType": "multiple", "obfuscated": false, "garbleargs": ""}