package api

import (
	"context"
	"database-manager/backend/internal/config"
	"database-manager/backend/internal/model"
	"database-manager/backend/internal/repository"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
)

// ConnectionAPI 连接管理API
type ConnectionAPI struct {
	configManager     *config.ConfigManager
	connectionManager *repository.ConnectionManager
}

// NewConnectionAPI 创建连接管理API
func NewConnectionAPI(configManager *config.ConfigManager, connectionManager *repository.ConnectionManager) *ConnectionAPI {
	return &ConnectionAPI{
		configManager:     configManager,
		connectionManager: connectionManager,
	}
}

// CreateConnectionRequest 创建连接请求
type CreateConnectionRequest struct {
	Name     string            `json:"name"`
	Type     string            `json:"type"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Username string            `json:"username"`
	Password string            `json:"password"`
	Database string            `json:"database"`
	SSL      bool              `json:"ssl"`
	Options  map[string]string `json:"options"`
}

// UpdateConnectionRequest 更新连接请求
type UpdateConnectionRequest struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Type     string            `json:"type"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Username string            `json:"username"`
	Password string            `json:"password"`
	Database string            `json:"database"`
	SSL      bool              `json:"ssl"`
	Options  map[string]string `json:"options"`
}

// ConnectionResponse 连接响应
type ConnectionResponse struct {
	ID        string            `json:"id"`
	Name      string            `json:"name"`
	Type      string            `json:"type"`
	Host      string            `json:"host"`
	Port      int               `json:"port"`
	Username  string            `json:"username"`
	Database  string            `json:"database"`
	SSL       bool              `json:"ssl"`
	Options   map[string]string `json:"options"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`
	Connected bool              `json:"connected"`
}

// APIResponse 通用API响应
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// CreateConnection 创建数据库连接
func (api *ConnectionAPI) CreateConnection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req CreateConnectionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if err := api.validateCreateRequest(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, err.Error())
		return
	}

	// 创建连接配置
	config := &model.ConnectionConfig{
		ID:       uuid.New().String(),
		Name:     req.Name,
		Type:     model.DatabaseType(req.Type),
		Host:     req.Host,
		Port:     req.Port,
		Username: req.Username,
		Password: req.Password,
		Database: req.Database,
		SSL:      req.SSL,
		Options:  req.Options,
	}

	// 测试连接
	if err := api.connectionManager.TestConnection(context.Background(), config); err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Connection test failed: %v", err))
		return
	}

	// 保存配置
	if err := api.configManager.SaveConnection(config); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to save connection: %v", err))
		return
	}

	// 添加到连接管理器
	api.connectionManager.AddConnection(config)

	// 返回响应
	response := api.configToResponse(config)
	api.writeSuccess(w, response, "Connection created successfully")
}

// GetConnections 获取所有连接
func (api *ConnectionAPI) GetConnections(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 获取所有连接配置
	configs := api.connectionManager.GetAllConfigs()

	// 获取连接状态
	statuses := api.connectionManager.GetConnectionStatus()

	// 转换为响应格式
	var responses []ConnectionResponse
	for _, config := range configs {
		response := api.configToResponse(config)
		if status, exists := statuses[config.ID]; exists {
			response.Connected = status.Connected
		}
		responses = append(responses, response)
	}

	api.writeSuccess(w, responses, "")
}

// GetConnection 获取单个连接
func (api *ConnectionAPI) GetConnection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从URL路径获取连接ID
	connectionID := r.URL.Query().Get("id")
	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}

	// 获取连接配置
	config, err := api.configManager.LoadConnection(connectionID)
	if err != nil {
		api.writeError(w, http.StatusNotFound, "Connection not found")
		return
	}

	// 获取连接状态
	statuses := api.connectionManager.GetConnectionStatus()
	response := api.configToResponse(config)
	if status, exists := statuses[connectionID]; exists {
		response.Connected = status.Connected
	}

	api.writeSuccess(w, response, "")
}

// UpdateConnection 更新连接配置
func (api *ConnectionAPI) UpdateConnection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req UpdateConnectionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}

	// 检查连接是否存在
	_, err := api.configManager.LoadConnection(req.ID)
	if err != nil {
		api.writeError(w, http.StatusNotFound, "Connection not found")
		return
	}

	// 创建更新的配置
	config := &model.ConnectionConfig{
		ID:       req.ID,
		Name:     req.Name,
		Type:     model.DatabaseType(req.Type),
		Host:     req.Host,
		Port:     req.Port,
		Username: req.Username,
		Password: req.Password,
		Database: req.Database,
		SSL:      req.SSL,
		Options:  req.Options,
	}

	// 测试连接
	if err := api.connectionManager.TestConnection(context.Background(), config); err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Connection test failed: %v", err))
		return
	}

	// 保存配置
	if err := api.configManager.SaveConnection(config); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to save connection: %v", err))
		return
	}

	// 更新连接管理器
	if err := api.connectionManager.UpdateConnection(config); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to update connection: %v", err))
		return
	}

	// 返回响应
	response := api.configToResponse(config)
	api.writeSuccess(w, response, "Connection updated successfully")
}

// DeleteConnection 删除连接
func (api *ConnectionAPI) DeleteConnection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从URL路径获取连接ID
	connectionID := r.URL.Query().Get("id")
	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}

	// 检查连接是否存在
	_, err := api.configManager.LoadConnection(connectionID)
	if err != nil {
		api.writeError(w, http.StatusNotFound, "Connection not found")
		return
	}

	// 从连接管理器中移除
	if err := api.connectionManager.RemoveConnection(context.Background(), connectionID); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to remove connection: %v", err))
		return
	}

	// 删除配置
	if err := api.configManager.DeleteConnection(connectionID); err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to delete connection: %v", err))
		return
	}

	api.writeSuccess(w, nil, "Connection deleted successfully")
}

// TestConnection 测试连接
func (api *ConnectionAPI) TestConnection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req CreateConnectionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if err := api.validateCreateRequest(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, err.Error())
		return
	}

	// 创建临时连接配置
	config := &model.ConnectionConfig{
		Name:     req.Name,
		Type:     model.DatabaseType(req.Type),
		Host:     req.Host,
		Port:     req.Port,
		Username: req.Username,
		Password: req.Password,
		Database: req.Database,
		SSL:      req.SSL,
		Options:  req.Options,
	}

	// 测试连接
	if err := api.connectionManager.TestConnection(context.Background(), config); err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Connection test failed: %v", err))
		return
	}

	api.writeSuccess(w, map[string]bool{"connected": true}, "Connection test successful")
}

// GetConnectionStatus 获取连接状态
func (api *ConnectionAPI) GetConnectionStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 获取所有连接状态
	statuses := api.connectionManager.GetConnectionStatus()
	api.writeSuccess(w, statuses, "")
}

// 辅助方法

// validateCreateRequest 验证创建连接请求
func (api *ConnectionAPI) validateCreateRequest(req *CreateConnectionRequest) error {
	if req.Name == "" {
		return fmt.Errorf("connection name is required")
	}
	if req.Type == "" {
		return fmt.Errorf("database type is required")
	}
	if req.Host == "" {
		return fmt.Errorf("host is required")
	}
	if req.Port <= 0 {
		return fmt.Errorf("valid port is required")
	}

	// 验证数据库类型
	switch model.DatabaseType(req.Type) {
	case model.DatabaseTypeRedis, model.DatabaseTypeMySQL, model.DatabaseTypePostgreSQL, model.DatabaseTypeMongoDB:
		// 有效类型
	default:
		return fmt.Errorf("unsupported database type: %s", req.Type)
	}

	return nil
}

// configToResponse 将配置转换为响应格式
func (api *ConnectionAPI) configToResponse(config *model.ConnectionConfig) ConnectionResponse {
	return ConnectionResponse{
		ID:        config.ID,
		Name:      config.Name,
		Type:      string(config.Type),
		Host:      config.Host,
		Port:      config.Port,
		Username:  config.Username,
		Database:  config.Database,
		SSL:       config.SSL,
		Options:   config.Options,
		CreatedAt: config.CreatedAt,
		UpdatedAt: config.UpdatedAt,
		Connected: false, // 默认值，调用方需要设置
	}
}

// writeSuccess 写入成功响应
func (api *ConnectionAPI) writeSuccess(w http.ResponseWriter, data interface{}, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := APIResponse{
		Success: true,
		Data:    data,
		Message: message,
	}

	json.NewEncoder(w).Encode(response)
}

// writeError 写入错误响应
func (api *ConnectionAPI) writeError(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := APIResponse{
		Success: false,
		Error:   message,
	}

	json.NewEncoder(w).Encode(response)
}
