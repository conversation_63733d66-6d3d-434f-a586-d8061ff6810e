<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleName</key>
    <string>Database Manager</string>
    
    <key>CFBundleDisplayName</key>
    <string>Database Manager</string>
    
    <key>CFBundleIdentifier</key>
    <string>com.databasemanager.app</string>
    
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    
    <key>CFBundleSignature</key>
    <string>DBMG</string>
    
    <key>CFBundleExecutable</key>
    <string>database-manager</string>
    
    <key>CFBundleIconFile</key>
    <string>icon.icns</string>
    
    <key>LSMinimumSystemVersion</key>
    <string>10.13</string>
    
    <key>NSHighResolutionCapable</key>
    <true/>
    
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    
    <!-- 文件关联配置 -->
    <key>CFBundleDocumentTypes</key>
    <array>
        <!-- SQL 文件 -->
        <dict>
            <key>CFBundleTypeName</key>
            <string>SQL Script</string>
            
            <key>CFBundleTypeRole</key>
            <string>Editor</string>
            
            <key>CFBundleTypeExtensions</key>
            <array>
                <string>sql</string>
            </array>
            
            <key>CFBundleTypeIconFile</key>
            <string>sql-icon.icns</string>
            
            <key>CFBundleTypeMIMETypes</key>
            <array>
                <string>text/sql</string>
                <string>application/sql</string>
            </array>
            
            <key>LSHandlerRank</key>
            <string>Owner</string>
            
            <key>LSItemContentTypes</key>
            <array>
                <string>public.sql</string>
            </array>
        </dict>
        
        <!-- Database Manager 配置文件 -->
        <dict>
            <key>CFBundleTypeName</key>
            <string>Database Manager Configuration</string>
            
            <key>CFBundleTypeRole</key>
            <string>Editor</string>
            
            <key>CFBundleTypeExtensions</key>
            <array>
                <string>dbm</string>
            </array>
            
            <key>CFBundleTypeIconFile</key>
            <string>config-icon.icns</string>
            
            <key>CFBundleTypeMIMETypes</key>
            <array>
                <string>application/json</string>
            </array>
            
            <key>LSHandlerRank</key>
            <string>Owner</string>
        </dict>
    </array>
    
    <!-- URL Schemes -->
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>Database Manager Protocol</string>
            
            <key>CFBundleURLSchemes</key>
            <array>
                <string>dbmanager</string>
            </array>
            
            <key>CFBundleURLIconFile</key>
            <string>icon.icns</string>
        </dict>
    </array>
    
    <!-- 导出的类型标识符 -->
    <key>UTExportedTypeDeclarations</key>
    <array>
        <dict>
            <key>UTTypeIdentifier</key>
            <string>com.databasemanager.config</string>
            
            <key>UTTypeDescription</key>
            <string>Database Manager Configuration File</string>
            
            <key>UTTypeConformsTo</key>
            <array>
                <string>public.json</string>
                <string>public.data</string>
            </array>
            
            <key>UTTypeTagSpecification</key>
            <dict>
                <key>public.filename-extension</key>
                <array>
                    <string>dbm</string>
                </array>
                
                <key>public.mime-type</key>
                <array>
                    <string>application/json</string>
                </array>
            </dict>
        </dict>
    </array>
    
    <!-- 权限配置 -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
    
    <!-- 网络权限 -->
    <key>NSNetworkVolumesUsageDescription</key>
    <string>Database Manager needs network access to connect to remote databases.</string>
    
    <!-- 文件访问权限 -->
    <key>NSDocumentsFolderUsageDescription</key>
    <string>Database Manager needs access to documents to save and load configuration files.</string>
    
    <key>NSDownloadsFolderUsageDescription</key>
    <string>Database Manager needs access to downloads to import configuration files.</string>
</dict>
</plist>
