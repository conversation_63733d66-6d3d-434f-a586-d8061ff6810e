import React, { useState } from 'react';
import { useI18n, Language } from '../../hooks/useI18n';
import { clsx } from 'clsx';

interface LanguageToggleProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const LanguageToggle: React.FC<LanguageToggleProps> = ({
  variant = 'dropdown',
  size = 'md',
  className
}) => {
  const { language, setLanguage, t } = useI18n();
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  // 语言选项
  const languageOptions: { value: Language; label: string; flag: string }[] = [
    {
      value: 'en',
      label: t('language.english'),
      flag: '🇺🇸',
    },
    {
      value: 'zh',
      label: t('language.chinese'),
      flag: '🇨🇳',
    },
  ];

  const currentLanguageOption = languageOptions.find(option => option.value === language);

  if (variant === 'button') {
    return (
      <button
        onClick={() => setLanguage(language === 'en' ? 'zh' : 'en')}
        className={clsx(
          'inline-flex items-center justify-center rounded-lg transition-colors duration-200',
          'bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600',
          'text-gray-700 dark:text-gray-300',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
          sizeClasses[size],
          className
        )}
        title={t('language.switchTo', { language: language === 'en' ? t('language.chinese') : t('language.english') })}
      >
        <span className={iconSizeClasses[size]}>
          {currentLanguageOption?.flag}
        </span>
      </button>
    );
  }

  return (
    <div className="relative">
      <button
        onClick={() => setDropdownOpen(!dropdownOpen)}
        className={clsx(
          'inline-flex items-center justify-center rounded-lg transition-colors duration-200',
          'bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600',
          'text-gray-700 dark:text-gray-300',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
          sizeClasses[size],
          className
        )}
        title={`${t('language.switchTo', { language: currentLanguageOption?.label || '' })}`}
      >
        <span className={iconSizeClasses[size]}>
          {currentLanguageOption?.flag}
        </span>
      </button>

      {dropdownOpen && (
        <>
          {/* 遮罩层 */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setDropdownOpen(false)}
          />

          {/* 下拉菜单 */}
          <div className="absolute right-0 mt-2 w-40 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-600 z-20">
            <div className="py-1">
              {languageOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => {
                    setLanguage(option.value);
                    setDropdownOpen(false);
                  }}
                  className={clsx(
                    'w-full flex items-center px-4 py-2 text-sm transition-colors',
                    'hover:bg-gray-100 dark:hover:bg-dark-700',
                    language === option.value
                      ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20'
                      : 'text-gray-700 dark:text-gray-300'
                  )}
                >
                  <span className="mr-3 text-lg">{option.flag}</span>
                  {option.label}
                  {language === option.value && (
                    <svg className="ml-auto w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageToggle;