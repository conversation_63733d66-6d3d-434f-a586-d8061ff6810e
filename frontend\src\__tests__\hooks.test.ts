import { renderHook, act } from '@testing-library/react';
import { useTheme } from '../hooks/useTheme';
import { useI18n } from '../hooks/useI18n';
import { useSettings } from '../hooks/useSettings';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe('useTheme Hook', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  test('should initialize with system theme by default', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useTheme());

    expect(result.current.theme).toBe('system');
    expect(typeof result.current.isDark).toBe('boolean');
    expect(typeof result.current.setTheme).toBe('function');
    expect(typeof result.current.toggleTheme).toBe('function');
  });

  test('should load saved theme from localStorage', () => {
    localStorageMock.getItem.mockReturnValue('dark');

    const { result } = renderHook(() => useTheme());

    expect(result.current.theme).toBe('dark');
  });

  test('should save theme to localStorage when changed', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useTheme());

    act(() => {
      result.current.setTheme('dark');
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith('database-manager-theme', 'dark');
    expect(result.current.theme).toBe('dark');
  });

  test('should toggle between light and dark themes', () => {
    localStorageMock.getItem.mockReturnValue('light');

    const { result } = renderHook(() => useTheme());

    act(() => {
      result.current.toggleTheme();
    });

    expect(result.current.theme).toBe('dark');

    act(() => {
      result.current.toggleTheme();
    });

    expect(result.current.theme).toBe('light');
  });
});

describe('useI18n Hook', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  test('should initialize with browser language', () => {
    localStorageMock.getItem.mockReturnValue(null);
    Object.defineProperty(navigator, 'language', {
      writable: true,
      value: 'en-US',
    });

    const { result } = renderHook(() => useI18n());

    expect(result.current.language).toBe('en');
    expect(typeof result.current.setLanguage).toBe('function');
    expect(typeof result.current.t).toBe('function');
  });

  test('should load saved language from localStorage', () => {
    localStorageMock.getItem.mockReturnValue('zh');

    const { result } = renderHook(() => useI18n());

    expect(result.current.language).toBe('zh');
  });

  test('should translate keys correctly', () => {
    localStorageMock.getItem.mockReturnValue('en');

    const { result } = renderHook(() => useI18n());

    const translation = result.current.t('common.save');
    expect(translation).toBe('Save');
  });

  test('should handle missing translation keys', () => {
    localStorageMock.getItem.mockReturnValue('en');

    const { result } = renderHook(() => useI18n());

    const translation = result.current.t('nonexistent.key');
    expect(translation).toBe('nonexistent.key');
  });

  test('should handle parameterized translations', () => {
    localStorageMock.getItem.mockReturnValue('en');

    const { result } = renderHook(() => useI18n());

    const translation = result.current.t('dataBrowser.showingEntries', {
      start: '1',
      end: '10',
      total: '100'
    });
    expect(translation).toBe('Showing 1 to 10 of 100 entries');
  });
});

describe('useSettings Hook', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  test('should initialize with default settings', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useSettings());

    expect(result.current.settings.theme).toBe('system');
    expect(result.current.settings.language).toBe('en');
    expect(result.current.settings.fontSize).toBe(14);
    expect(typeof result.current.updateSettings).toBe('function');
    expect(typeof result.current.resetSettings).toBe('function');
  });

  test('should load saved settings from localStorage', () => {
    const savedSettings = {
      theme: 'dark',
      language: 'zh',
      fontSize: 16,
    };
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedSettings));

    const { result } = renderHook(() => useSettings());

    expect(result.current.settings.theme).toBe('dark');
    expect(result.current.settings.language).toBe('zh');
    expect(result.current.settings.fontSize).toBe(16);
  });

  test('should update settings and save to localStorage', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useSettings());

    act(() => {
      result.current.updateSettings({ fontSize: 18 });
    });

    expect(result.current.settings.fontSize).toBe(18);
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'database-manager-settings',
      expect.stringContaining('"fontSize":18')
    );
  });

  test('should reset settings to defaults', () => {
    const savedSettings = {
      theme: 'dark',
      language: 'zh',
      fontSize: 20,
    };
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedSettings));

    const { result } = renderHook(() => useSettings());

    expect(result.current.settings.fontSize).toBe(20);

    act(() => {
      result.current.resetSettings();
    });

    expect(result.current.settings.fontSize).toBe(14);
    expect(result.current.settings.theme).toBe('system');
    expect(result.current.settings.language).toBe('en');
  });

  test('should export and import settings', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useSettings());

    // Export settings
    const exported = result.current.exportSettings();
    expect(typeof exported).toBe('string');

    // Import settings
    const importResult = result.current.importSettings(exported);
    expect(importResult).toBe(true);
  });

  test('should handle invalid import data', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useSettings());

    const importResult = result.current.importSettings('invalid json');
    expect(importResult).toBe(false);
  });
});