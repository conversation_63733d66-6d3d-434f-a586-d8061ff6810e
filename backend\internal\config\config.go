package config

import (
	"database-manager/backend/internal/model"
	"database-manager/backend/pkg/crypto"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
)

// ConfigManager 配置管理器
type ConfigManager struct {
	configDir   string
	encryptor   *crypto.Encryptor
	connections map[string]*model.ConnectionConfig
	mutex       sync.RWMutex
}

// NewConfigManager 创建配置管理器
func NewConfigManager(configDir string) (*ConfigManager, error) {
	// 确保配置目录存在
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %w", err)
	}

	// 创建加密器
	encryptor, err := crypto.NewEncryptor()
	if err != nil {
		return nil, fmt.Errorf("failed to create encryptor: %w", err)
	}

	cm := &ConfigManager{
		configDir:   configDir,
		encryptor:   encryptor,
		connections: make(map[string]*model.ConnectionConfig),
	}

	// 加载现有配置
	if err := cm.loadConnections(); err != nil {
		return nil, fmt.Errorf("failed to load connections: %w", err)
	}

	return cm, nil
}

// SaveConnection 保存连接配置
func (cm *ConfigManager) SaveConnection(config *model.ConnectionConfig) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 加密密码
	encryptedConfig := *config
	if config.Password != "" {
		encryptedPassword, err := cm.encryptor.Encrypt(config.Password)
		if err != nil {
			return fmt.Errorf("failed to encrypt password: %w", err)
		}
		encryptedConfig.Password = encryptedPassword
	}

	// 序列化配置
	data, err := json.MarshalIndent(encryptedConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// 保存到文件
	filename := filepath.Join(cm.configDir, config.ID+".json")
	if err := os.WriteFile(filename, data, 0600); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	// 更新内存中的配置（保存原始密码）
	cm.connections[config.ID] = config

	return nil
}

// LoadConnection 加载连接配置
func (cm *ConfigManager) LoadConnection(id string) (*model.ConnectionConfig, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	config, exists := cm.connections[id]
	if !exists {
		return nil, fmt.Errorf("connection not found: %s", id)
	}

	return config, nil
}

// LoadAllConnections 加载所有连接配置
func (cm *ConfigManager) LoadAllConnections() ([]*model.ConnectionConfig, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	configs := make([]*model.ConnectionConfig, 0, len(cm.connections))
	for _, config := range cm.connections {
		configs = append(configs, config)
	}

	return configs, nil
}

// DeleteConnection 删除连接配置
func (cm *ConfigManager) DeleteConnection(id string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 删除文件
	filename := filepath.Join(cm.configDir, id+".json")
	if err := os.Remove(filename); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete config file: %w", err)
	}

	// 从内存中删除
	delete(cm.connections, id)

	return nil
}

// loadConnections 加载所有连接配置
func (cm *ConfigManager) loadConnections() error {
	// 读取配置目录中的所有JSON文件
	files, err := filepath.Glob(filepath.Join(cm.configDir, "*.json"))
	if err != nil {
		return fmt.Errorf("failed to glob config files: %w", err)
	}

	for _, file := range files {
		if err := cm.loadConnectionFromFile(file); err != nil {
			// 记录错误但继续加载其他文件
			fmt.Printf("Warning: failed to load config from %s: %v\n", file, err)
		}
	}

	return nil
}

// loadConnectionFromFile 从文件加载连接配置
func (cm *ConfigManager) loadConnectionFromFile(filename string) error {
	// 读取文件
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// 反序列化配置
	var config model.ConnectionConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 解密密码
	if config.Password != "" {
		decryptedPassword, err := cm.encryptor.Decrypt(config.Password)
		if err != nil {
			return fmt.Errorf("failed to decrypt password: %w", err)
		}
		config.Password = decryptedPassword
	}

	// 添加到内存
	cm.connections[config.ID] = &config

	return nil
}

// ExportConnections 导出连接配置（不包含密码）
func (cm *ConfigManager) ExportConnections() ([]*model.ConnectionConfig, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	configs := make([]*model.ConnectionConfig, 0, len(cm.connections))
	for _, config := range cm.connections {
		// 创建副本并清除密码
		exportConfig := *config
		exportConfig.Password = ""
		configs = append(configs, &exportConfig)
	}

	return configs, nil
}

// ImportConnections 导入连接配置
func (cm *ConfigManager) ImportConnections(configs []*model.ConnectionConfig) error {
	for _, config := range configs {
		if err := cm.SaveConnection(config); err != nil {
			return fmt.Errorf("failed to import connection %s: %w", config.ID, err)
		}
	}

	return nil
}

// GetConfigDir 获取配置目录
func (cm *ConfigManager) GetConfigDir() string {
	return cm.configDir
}
