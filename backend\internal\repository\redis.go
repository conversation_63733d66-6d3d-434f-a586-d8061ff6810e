package repository

import (
	"context"
	"database-manager/backend/internal/model"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
)

// RedisRepository Redis连接器实现
type RedisRepository struct {
	client    *redis.Client
	config    *model.ConnectionConfig
	connected bool
}

// NewRedisRepository 创建Redis连接器
func NewRedisRepository() *RedisRepository {
	return &RedisRepository{
		connected: false,
	}
}

// Connect 连接Redis数据库
func (r *RedisRepository) Connect(ctx context.Context, config *model.ConnectionConfig) error {
	r.config = config

	// 构建Redis连接选项
	options := &redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password: config.Password,
		DB:       0, // 默认数据库
	}

	// 解析数据库编号
	if config.Database != "" {
		if db, err := strconv.Atoi(config.Database); err == nil {
			options.DB = db
		}
	}

	// 处理SSL连接
	if config.SSL {
		// Redis通常使用TLS，这里可以添加TLS配置
		// options.TLSConfig = &tls.Config{}
	}

	// 处理其他选项
	if config.Options != nil {
		if timeout, ok := config.Options["timeout"]; ok {
			if t, err := time.ParseDuration(timeout); err == nil {
				options.DialTimeout = t
				options.ReadTimeout = t
				options.WriteTimeout = t
			}
		}

		if poolSize, ok := config.Options["pool_size"]; ok {
			if size, err := strconv.Atoi(poolSize); err == nil {
				options.PoolSize = size
			}
		}
	}

	// 创建Redis客户端
	r.client = redis.NewClient(options)

	// 测试连接
	if err := r.Ping(ctx); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	r.connected = true
	return nil
}

// Disconnect 断开连接
func (r *RedisRepository) Disconnect(ctx context.Context) error {
	if r.client != nil {
		err := r.client.Close()
		r.client = nil
		r.connected = false
		return err
	}
	return nil
}

// Ping 测试连接
func (r *RedisRepository) Ping(ctx context.Context) error {
	if r.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	_, err := r.client.Ping(ctx).Result()
	return err
}

// IsConnected 检查连接状态
func (r *RedisRepository) IsConnected() bool {
	return r.connected && r.client != nil
}

// GetDatabaseInfo 获取数据库信息
func (r *RedisRepository) GetDatabaseInfo(ctx context.Context) (*model.DatabaseInfo, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	// 获取Redis信息
	info, err := r.client.Info(ctx, "server", "keyspace").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis info: %w", err)
	}

	// 解析版本信息
	version := "unknown"
	lines := strings.Split(info, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "redis_version:") {
			version = strings.TrimPrefix(line, "redis_version:")
			version = strings.TrimSpace(version)
			break
		}
	}

	// 获取键数量
	dbSize, err := r.client.DBSize(ctx).Result()
	if err != nil {
		dbSize = 0
	}

	return &model.DatabaseInfo{
		Name:    fmt.Sprintf("Redis DB %s", r.config.Database),
		Size:    0,           // Redis不直接提供数据库大小
		Tables:  int(dbSize), // 使用键数量作为"表"数量
		Version: version,
	}, nil
}

// GetTables 获取表列表 (Redis中为键列表)
func (r *RedisRepository) GetTables(ctx context.Context, database string) ([]*model.TableInfo, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	// Redis没有表的概念，返回空列表
	return []*model.TableInfo{}, nil
}

// GetColumns 获取表列信息 (Redis中不适用)
func (r *RedisRepository) GetColumns(ctx context.Context, database, table string) ([]*model.ColumnInfo, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	// Redis没有列的概念，返回空列表
	return []*model.ColumnInfo{}, nil
}

// ExecuteQuery 执行查询 (Redis中执行命令)
func (r *RedisRepository) ExecuteQuery(ctx context.Context, query string, params ...any) (*model.QueryResult, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	start := time.Now()

	// 解析Redis命令
	parts := strings.Fields(query)
	if len(parts) == 0 {
		return nil, fmt.Errorf("empty command")
	}

	// 执行Redis命令
	args := make([]interface{}, len(parts))
	for i, part := range parts {
		args[i] = part
	}
	cmd := r.client.Do(ctx, args...)
	result, err := cmd.Result()
	elapsed := time.Since(start)

	if err != nil {
		return &model.QueryResult{
			Columns: []string{"Error"},
			Rows:    [][]any{},
			Total:   0,
			Elapsed: elapsed,
			Error:   err.Error(),
		}, nil
	}

	// 格式化结果
	columns := []string{"Result"}
	rows := [][]any{{result}}

	return &model.QueryResult{
		Columns: columns,
		Rows:    rows,
		Total:   1,
		Elapsed: elapsed,
	}, nil
}

// ExecuteCommand 执行命令
func (r *RedisRepository) ExecuteCommand(ctx context.Context, command string, params ...any) error {
	if !r.IsConnected() {
		return fmt.Errorf("not connected to Redis")
	}

	// 解析命令
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return fmt.Errorf("empty command")
	}

	// 执行命令
	args := make([]interface{}, len(parts))
	for i, part := range parts {
		args[i] = part
	}
	_, err := r.client.Do(ctx, args...).Result()
	return err
}

// GetKeys 获取键列表
func (r *RedisRepository) GetKeys(ctx context.Context, pattern string, limit int) ([]string, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	if pattern == "" {
		pattern = "*"
	}

	// 使用SCAN命令获取键列表，避免阻塞
	var keys []string
	var cursor uint64

	for {
		var scanKeys []string
		var err error

		scanKeys, cursor, err = r.client.Scan(ctx, cursor, pattern, int64(limit)).Result()
		if err != nil {
			return nil, fmt.Errorf("failed to scan keys: %w", err)
		}

		keys = append(keys, scanKeys...)

		if cursor == 0 || len(keys) >= limit {
			break
		}
	}

	// 限制返回数量
	if len(keys) > limit {
		keys = keys[:limit]
	}

	return keys, nil
}

// GetKeyInfo 获取键信息
func (r *RedisRepository) GetKeyInfo(ctx context.Context, key string) (*model.RedisKeyInfo, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	// 获取键类型
	keyType, err := r.client.Type(ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get key type: %w", err)
	}

	// 获取TTL
	ttl, err := r.client.TTL(ctx, key).Result()
	if err != nil {
		ttl = -1
	}

	// 获取内存使用
	memUsage, err := r.client.MemoryUsage(ctx, key).Result()
	if err != nil {
		memUsage = 0
	}

	return &model.RedisKeyInfo{
		Key:  key,
		Type: keyType,
		TTL:  int64(ttl.Seconds()),
		Size: memUsage,
	}, nil
}

// GetValue 获取键值
func (r *RedisRepository) GetValue(ctx context.Context, key string) (any, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	// 获取键类型
	keyType, err := r.client.Type(ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get key type: %w", err)
	}

	// 根据类型获取值
	switch keyType {
	case "string":
		return r.client.Get(ctx, key).Result()
	case "list":
		return r.client.LRange(ctx, key, 0, -1).Result()
	case "set":
		return r.client.SMembers(ctx, key).Result()
	case "zset":
		return r.client.ZRangeWithScores(ctx, key, 0, -1).Result()
	case "hash":
		return r.client.HGetAll(ctx, key).Result()
	default:
		return nil, fmt.Errorf("unsupported key type: %s", keyType)
	}
}

// SetValue 设置键值
func (r *RedisRepository) SetValue(ctx context.Context, key string, value any, ttl int64) error {
	if !r.IsConnected() {
		return fmt.Errorf("not connected to Redis")
	}

	// 设置值
	if ttl > 0 {
		return r.client.Set(ctx, key, value, time.Duration(ttl)*time.Second).Err()
	}
	return r.client.Set(ctx, key, value, 0).Err()
}

// DeleteKey 删除键
func (r *RedisRepository) DeleteKey(ctx context.Context, key string) error {
	if !r.IsConnected() {
		return fmt.Errorf("not connected to Redis")
	}

	return r.client.Del(ctx, key).Err()
}

// GetDatabases 获取数据库列表
func (r *RedisRepository) GetDatabases(ctx context.Context) ([]int, error) {
	if !r.IsConnected() {
		return nil, fmt.Errorf("not connected to Redis")
	}

	// Redis默认有16个数据库 (0-15)
	databases := make([]int, 16)
	for i := 0; i < 16; i++ {
		databases[i] = i
	}

	return databases, nil
}

// SelectDatabase 选择数据库
func (r *RedisRepository) SelectDatabase(ctx context.Context, db int) error {
	if !r.IsConnected() {
		return fmt.Errorf("not connected to Redis")
	}

	// 创建新的客户端连接到指定数据库
	options := r.client.Options()
	options.DB = db

	// 关闭当前连接
	r.client.Close()

	// 创建新连接
	r.client = redis.NewClient(options)

	// 测试新连接
	return r.Ping(ctx)
}
