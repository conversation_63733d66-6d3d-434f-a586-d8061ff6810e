@echo off
REM 构建版本信息脚本 (Windows)
REM 用于在构建时设置版本信息

setlocal enabledelayedexpansion

REM 获取版本信息
if "%VERSION%"=="" (
    for /f "tokens=*" %%i in ('git describe --tags --always --dirty 2^>nul') do set VERSION=%%i
    if "!VERSION!"=="" set VERSION=1.0.0-dev
)

REM 获取构建时间
for /f "tokens=*" %%i in ('powershell -command "Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC'"') do set BUILD_TIME=%%i

REM 获取Git信息
for /f "tokens=*" %%i in ('git rev-parse HEAD 2^>nul') do set GIT_COMMIT=%%i
if "!GIT_COMMIT!"=="" set GIT_COMMIT=unknown

for /f "tokens=*" %%i in ('git rev-parse --abbrev-ref HEAD 2^>nul') do set GIT_BRANCH=%%i
if "!GIT_BRANCH!"=="" set GIT_BRANCH=unknown

REM 获取Go版本
for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i

REM 移除版本号前缀 'v'
if "!VERSION:~0,1!"=="v" set VERSION=!VERSION:~1!

echo Building with version information:
echo   Version: !VERSION!
echo   Build Time: !BUILD_TIME!
echo   Git Commit: !GIT_COMMIT!
echo   Git Branch: !GIT_BRANCH!
echo   Go Version: !GO_VERSION!

REM 构建ldflags
set LDFLAGS=-X "database-manager/internal/version.Version=!VERSION!"
set LDFLAGS=!LDFLAGS! -X "database-manager/internal/version.BuildTime=!BUILD_TIME!"
set LDFLAGS=!LDFLAGS! -X "database-manager/internal/version.GitCommit=!GIT_COMMIT!"
set LDFLAGS=!LDFLAGS! -X "database-manager/internal/version.GitBranch=!GIT_BRANCH!"

REM 如果是发布版本，添加优化标志
echo !VERSION! | findstr /C:"dev" >nul
if errorlevel 1 (
    echo !VERSION! | findstr /C:"alpha" >nul
    if errorlevel 1 (
        echo !VERSION! | findstr /C:"beta" >nul
        if errorlevel 1 (
            set LDFLAGS=!LDFLAGS! -s -w
        )
    )
)

echo LDFLAGS: !LDFLAGS!

REM 导出变量
set BUILD_LDFLAGS=!LDFLAGS!
set BUILD_VERSION=!VERSION!

REM 如果传入了构建命令，执行它
if not "%~1"=="" (
    echo Executing: %*
    %*
)

endlocal
