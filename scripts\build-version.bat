@echo off
REM Build version information script for Windows
REM Used to set version information during build

setlocal enabledelayedexpansion

REM Get version information
if "%VERSION%"=="" (
    for /f "tokens=*" %%i in ('git describe --tags --always --dirty 2^>nul') do set VERSION=%%i
    if "!VERSION!"=="" set VERSION=1.0.0-dev
)

REM Get build time
for /f "tokens=*" %%i in ('powershell -command "Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC'"') do set BUILD_TIME=%%i

REM Get Git information
for /f "tokens=*" %%i in ('git rev-parse HEAD 2^>nul') do set GIT_COMMIT=%%i
if "!GIT_COMMIT!"=="" set GIT_COMMIT=unknown

for /f "tokens=*" %%i in ('git rev-parse --abbrev-ref HEAD 2^>nul') do set GIT_BRANCH=%%i
if "!GIT_BRANCH!"=="" set GIT_BRANCH=unknown

REM Get Go version
for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i

REM Remove version prefix 'v'
if "!VERSION:~0,1!"=="v" set VERSION=!VERSION:~1!

echo Building with version information:
echo   Version: !VERSION!
echo   Build Time: !BUILD_TIME!
echo   Git Commit: !GIT_COMMIT!
echo   Git Branch: !GIT_BRANCH!
echo   Go Version: !GO_VERSION!

REM Build ldflags
set LDFLAGS=-X "database-manager/internal/version.Version=!VERSION!"
set LDFLAGS=!LDFLAGS! -X "database-manager/internal/version.BuildTime=!BUILD_TIME!"
set LDFLAGS=!LDFLAGS! -X "database-manager/internal/version.GitCommit=!GIT_COMMIT!"
set LDFLAGS=!LDFLAGS! -X "database-manager/internal/version.GitBranch=!GIT_BRANCH!"

REM Add optimization flags for release versions
echo !VERSION! | findstr /C:"dev" >nul
if errorlevel 1 (
    echo !VERSION! | findstr /C:"alpha" >nul
    if errorlevel 1 (
        echo !VERSION! | findstr /C:"beta" >nul
        if errorlevel 1 (
            set LDFLAGS=!LDFLAGS! -s -w
        )
    )
)

echo LDFLAGS: !LDFLAGS!

REM Export variables
set BUILD_LDFLAGS=!LDFLAGS!
set BUILD_VERSION=!VERSION!

REM Execute build command if provided
if not "%~1"=="" (
    echo Executing: %*
    %*
)

endlocal
