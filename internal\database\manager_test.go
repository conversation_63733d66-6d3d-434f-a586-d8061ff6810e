package database

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestManager_SaveAndLoadConnections(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "db_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "connections.json")
	manager := NewManager(configPath)

	// 创建测试连接配置
	connections := []ConnectionConfig{
		{
			ID:       "test1",
			Name:     "Test Redis",
			Type:     "redis",
			Host:     "localhost",
			Port:     "6379",
			Database: "0",
		},
		{
			ID:       "test2",
			Name:     "Test MySQL",
			Type:     "mysql",
			Host:     "localhost",
			Port:     "3306",
			Username: "root",
			Password: "password",
			Database: "testdb",
		},
	}

	// 保存连接配置
	for _, conn := range connections {
		err := manager.SaveConnection(conn)
		assert.NoError(t, err)
	}

	// 加载连接配置
	loadedConnections, err := manager.GetConnections()
	assert.NoError(t, err)
	assert.Len(t, loadedConnections, 2)

	// 验证连接配置
	assert.Equal(t, "test1", loadedConnections[0].ID)
	assert.Equal(t, "Test Redis", loadedConnections[0].Name)
	assert.Equal(t, "redis", loadedConnections[0].Type)

	assert.Equal(t, "test2", loadedConnections[1].ID)
	assert.Equal(t, "Test MySQL", loadedConnections[1].Name)
	assert.Equal(t, "mysql", loadedConnections[1].Type)
}

func TestManager_DeleteConnection(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "db_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "connections.json")
	manager := NewManager(configPath)

	// 添加测试连接
	conn := ConnectionConfig{
		ID:   "test1",
		Name: "Test Connection",
		Type: "redis",
		Host: "localhost",
		Port: "6379",
	}

	err = manager.SaveConnection(conn)
	require.NoError(t, err)

	// 验证连接已保存
	connections, err := manager.GetConnections()
	require.NoError(t, err)
	assert.Len(t, connections, 1)

	// 删除连接
	err = manager.DeleteConnection("test1")
	assert.NoError(t, err)

	// 验证连接已删除
	connections, err = manager.GetConnections()
	assert.NoError(t, err)
	assert.Len(t, connections, 0)
}

func TestManager_GetConnection(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "db_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "connections.json")
	manager := NewManager(configPath)

	// 添加测试连接
	conn := ConnectionConfig{
		ID:   "test1",
		Name: "Test Connection",
		Type: "redis",
		Host: "localhost",
		Port: "6379",
	}

	err = manager.SaveConnection(conn)
	require.NoError(t, err)

	// 获取连接
	retrievedConn, err := manager.GetConnection("test1")
	assert.NoError(t, err)
	assert.Equal(t, conn.ID, retrievedConn.ID)
	assert.Equal(t, conn.Name, retrievedConn.Name)
	assert.Equal(t, conn.Type, retrievedConn.Type)

	// 获取不存在的连接
	_, err = manager.GetConnection("nonexistent")
	assert.Error(t, err)
}

func TestManager_UpdateConnection(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "db_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "connections.json")
	manager := NewManager(configPath)

	// 添加测试连接
	conn := ConnectionConfig{
		ID:   "test1",
		Name: "Original Name",
		Type: "redis",
		Host: "localhost",
		Port: "6379",
	}

	err = manager.SaveConnection(conn)
	require.NoError(t, err)

	// 更新连接
	updatedConn := ConnectionConfig{
		ID:   "test1",
		Name: "Updated Name",
		Type: "redis",
		Host: "updated-host",
		Port: "6380",
	}

	err = manager.SaveConnection(updatedConn)
	assert.NoError(t, err)

	// 验证更新
	retrievedConn, err := manager.GetConnection("test1")
	assert.NoError(t, err)
	assert.Equal(t, "Updated Name", retrievedConn.Name)
	assert.Equal(t, "updated-host", retrievedConn.Host)
	assert.Equal(t, "6380", retrievedConn.Port)

	// 验证只有一个连接
	connections, err := manager.GetConnections()
	assert.NoError(t, err)
	assert.Len(t, connections, 1)
}

func TestManager_CreateConnector(t *testing.T) {
	manager := NewManager("")

	// 测试Redis连接器
	redisConnector := manager.CreateConnector("redis")
	assert.NotNil(t, redisConnector)
	assert.IsType(t, &RedisConnector{}, redisConnector)

	// 测试MySQL连接器
	mysqlConnector := manager.CreateConnector("mysql")
	assert.NotNil(t, mysqlConnector)
	assert.IsType(t, &MySQLConnector{}, mysqlConnector)

	// 测试PostgreSQL连接器
	pgConnector := manager.CreateConnector("postgresql")
	assert.NotNil(t, pgConnector)
	assert.IsType(t, &PostgreSQLConnector{}, pgConnector)

	// 测试MongoDB连接器
	mongoConnector := manager.CreateConnector("mongodb")
	assert.NotNil(t, mongoConnector)
	assert.IsType(t, &MongoConnector{}, mongoConnector)

	// 测试不支持的类型
	unknownConnector := manager.CreateConnector("unknown")
	assert.Nil(t, unknownConnector)
}

func TestManager_FileOperations(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "db_manager_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "connections.json")
	manager := NewManager(configPath)

	// 测试空文件加载
	connections, err := manager.GetConnections()
	assert.NoError(t, err)
	assert.Len(t, connections, 0)

	// 添加连接并验证文件创建
	conn := ConnectionConfig{
		ID:   "test1",
		Name: "Test Connection",
		Type: "redis",
		Host: "localhost",
		Port: "6379",
	}

	err = manager.SaveConnection(conn)
	assert.NoError(t, err)

	// 验证文件存在
	_, err = os.Stat(configPath)
	assert.NoError(t, err)

	// 创建新的管理器实例并验证数据持久化
	newManager := NewManager(configPath)
	loadedConnections, err := newManager.GetConnections()
	assert.NoError(t, err)
	assert.Len(t, loadedConnections, 1)
	assert.Equal(t, conn.ID, loadedConnections[0].ID)
}
