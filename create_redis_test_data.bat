@echo off
echo Creating Redis test data...

REM Check if redis-cli is available
redis-cli --version >nul 2>&1
if errorlevel 1 (
    echo Redis CLI not found. Please install Redis or add redis-cli to PATH.
    pause
    exit /b 1
)

echo.
echo Creating test data in Redis...

REM String types
redis-cli SET user:1:name "<PERSON>"
redis-cli SET user:1:email "<EMAIL>"
redis-cli SET user:2:name "<PERSON>"
redis-cli SET user:2:email "<EMAIL>"
redis-cli SET session:abc123 "user_session_data"
redis-cli SET cache:products "product_list_cache"

REM List types
redis-cli LPUSH user:1:orders "order1" "order2" "order3"
redis-cli LPUSH user:2:orders "order4" "order5"
redis-cli LPUSH queue:emails "<EMAIL>" "<EMAIL>"

REM Set types
redis-cli SADD user:1:tags "developer" "golang" "redis"
redis-cli SADD user:2:tags "designer" "ui" "ux"
redis-cli SADD categories:tech "programming" "databases" "web"

REM Hash types
redis-cli HSET user:1:profile name "<PERSON>" age "30" city "New York"
redis-cli HSET user:2:profile name "Jane" age "28" city "San Francisco"
redis-cli HSET config:app debug "true" version "1.0.0" env "development"

REM ZSet types
redis-cli ZADD leaderboard 100 "player1" 200 "player2" 150 "player3"
redis-cli ZADD scores:game1 85.5 "user1" 92.3 "user2" 78.9 "user3"

REM Set some TTL values
redis-cli EXPIRE session:abc123 3600
redis-cli EXPIRE cache:products 1800
redis-cli EXPIRE user:1:orders 7200

REM Create data in different databases
redis-cli SELECT 1
redis-cli SET db1:test:key1 "value in database 1"
redis-cli SET db1:test:key2 "another value in db1"

redis-cli SELECT 2
redis-cli SET db2:config:setting1 "production"
redis-cli SET db2:config:setting2 "enabled"

REM Switch back to database 0
redis-cli SELECT 0

echo.
echo Test data created successfully!
echo.
echo Created data includes:
echo - String keys: user:*, session:*, cache:*
echo - List keys: user:*:orders, queue:*
echo - Set keys: user:*:tags, categories:*
echo - Hash keys: user:*:profile, config:*
echo - ZSet keys: leaderboard, scores:*
echo - Data in DB 1 and DB 2
echo - Some keys with TTL set
echo.
echo You can now test the Redis Manager interface!
pause
