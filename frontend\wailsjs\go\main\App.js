// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CenterWindow() {
  return window['go']['main']['App']['CenterWindow']();
}

export function CheckForUpdates() {
  return window['go']['main']['App']['CheckForUpdates']();
}

export function DeleteConnection(arg1) {
  return window['go']['main']['App']['DeleteConnection'](arg1);
}

export function ExecuteQuery(arg1, arg2) {
  return window['go']['main']['App']['ExecuteQuery'](arg1, arg2);
}

export function ForceGarbageCollection() {
  return window['go']['main']['App']['ForceGarbageCollection']();
}

export function GetBuildInfo() {
  return window['go']['main']['App']['GetBuildInfo']();
}

export function GetConnections() {
  return window['go']['main']['App']['GetConnections']();
}

export function GetFullVersionString() {
  return window['go']['main']['App']['GetFullVersionString']();
}

export function GetMemoryStats() {
  return window['go']['main']['App']['GetMemoryStats']();
}

export function GetSupportedFileTypes() {
  return window['go']['main']['App']['GetSupportedFileTypes']();
}

export function GetSystemTraySettings() {
  return window['go']['main']['App']['GetSystemTraySettings']();
}

export function GetTrayMenuItems() {
  return window['go']['main']['App']['GetTrayMenuItems']();
}

export function GetVersion() {
  return window['go']['main']['App']['GetVersion']();
}

export function GetVersionString() {
  return window['go']['main']['App']['GetVersionString']();
}

export function GetWindowSettings() {
  return window['go']['main']['App']['GetWindowSettings']();
}

export function GetWindowState() {
  return window['go']['main']['App']['GetWindowState']();
}

export function HandleCommandLineArgs(arg1) {
  return window['go']['main']['App']['HandleCommandLineArgs'](arg1);
}

export function HandleTrayMenuClick(arg1) {
  return window['go']['main']['App']['HandleTrayMenuClick'](arg1);
}

export function HideWindow() {
  return window['go']['main']['App']['HideWindow']();
}

export function IsDevVersion() {
  return window['go']['main']['App']['IsDevVersion']();
}

export function MaximizeWindow() {
  return window['go']['main']['App']['MaximizeWindow']();
}

export function MinimizeToTray() {
  return window['go']['main']['App']['MinimizeToTray']();
}

export function MinimizeWindow() {
  return window['go']['main']['App']['MinimizeWindow']();
}

export function OpenFile(arg1) {
  return window['go']['main']['App']['OpenFile'](arg1);
}

export function OptimizeMemory() {
  return window['go']['main']['App']['OptimizeMemory']();
}

export function QuitApplication() {
  return window['go']['main']['App']['QuitApplication']();
}

export function RegisterFileAssociations() {
  return window['go']['main']['App']['RegisterFileAssociations']();
}

export function RestoreFromTray() {
  return window['go']['main']['App']['RestoreFromTray']();
}

export function RestoreWindow() {
  return window['go']['main']['App']['RestoreWindow']();
}

export function SaveConnection(arg1) {
  return window['go']['main']['App']['SaveConnection'](arg1);
}

export function SetGCInterval(arg1) {
  return window['go']['main']['App']['SetGCInterval'](arg1);
}

export function SetMemoryThreshold(arg1) {
  return window['go']['main']['App']['SetMemoryThreshold'](arg1);
}

export function SetSystemTraySettings(arg1) {
  return window['go']['main']['App']['SetSystemTraySettings'](arg1);
}

export function SetWindowAlwaysOnTop(arg1) {
  return window['go']['main']['App']['SetWindowAlwaysOnTop'](arg1);
}

export function SetWindowPosition(arg1, arg2) {
  return window['go']['main']['App']['SetWindowPosition'](arg1, arg2);
}

export function SetWindowSize(arg1, arg2) {
  return window['go']['main']['App']['SetWindowSize'](arg1, arg2);
}

export function SetWindowTitle(arg1) {
  return window['go']['main']['App']['SetWindowTitle'](arg1);
}

export function ShowNotification(arg1, arg2) {
  return window['go']['main']['App']['ShowNotification'](arg1, arg2);
}

export function ShowWindow() {
  return window['go']['main']['App']['ShowWindow']();
}

export function TestConnection(arg1) {
  return window['go']['main']['App']['TestConnection'](arg1);
}

export function ToggleMaximize() {
  return window['go']['main']['App']['ToggleMaximize']();
}

export function UnregisterFileAssociations() {
  return window['go']['main']['App']['UnregisterFileAssociations']();
}
