package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net"
	"strings"
	"sync"
	"time"

	"database-manager/internal/memory"
	"database-manager/internal/version"

	"github.com/google/uuid"
	"github.com/wailsapp/wails/v2/pkg/runtime"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	// 数据库驱动
	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
)

// App struct
type App struct {
	ctx           context.Context
	connections   map[string]*Connection
	mutex         sync.RWMutex
	memoryManager *memory.Manager
}

// Connection represents a database connection
type Connection struct {
	ID        string            `json:"id"`
	Name      string            `json:"name"`
	Type      string            `json:"type"`
	Host      string            `json:"host"`
	Port      int               `json:"port"`
	Username  string            `json:"username"`
	Password  string            `json:"password"`
	Database  string            `json:"database"`
	SSL       bool              `json:"ssl"`
	Options   map[string]string `json:"options"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`
	Connected bool              `json:"connected"`
}

// QueryResult represents query execution result
type QueryResult struct {
	Columns []string `json:"columns"`
	Rows    [][]any  `json:"rows"`
	Count   int      `json:"count"`
	Error   string   `json:"error,omitempty"`
}

// NewApp creates a new App application struct
func NewApp() *App {
	app := &App{
		connections:   make(map[string]*Connection),
		memoryManager: memory.NewManager(),
	}

	// 添加清理函数
	app.memoryManager.AddCleanupFunc(func() {
		app.cleanupConnections()
	})

	return app
}

// startup is called when the app starts. The context passed
// in is saved and can be used to call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	log.Println("Database Manager started successfully")

	// 加载示例连接
	a.loadSampleConnections()
}

// loadSampleConnections 加载示例连接
func (a *App) loadSampleConnections() {
	sampleConnections := []*Connection{
		{
			ID:        uuid.New().String(),
			Name:      "Local Redis",
			Type:      "redis",
			Host:      "localhost",
			Port:      6379,
			Username:  "",
			Password:  "",
			Database:  "0",
			SSL:       false,
			Options:   make(map[string]string),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Connected: false,
		},
		{
			ID:        uuid.New().String(),
			Name:      "Local MySQL",
			Type:      "mysql",
			Host:      "localhost",
			Port:      3306,
			Username:  "root",
			Password:  "",
			Database:  "test",
			SSL:       false,
			Options:   make(map[string]string),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Connected: false,
		},
	}

	a.mutex.Lock()
	defer a.mutex.Unlock()

	for _, conn := range sampleConnections {
		a.connections[conn.ID] = conn
	}
}

// Wails前端调用的方法

// GetConnections 获取所有连接
func (a *App) GetConnections() []*Connection {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	var result []*Connection
	for _, conn := range a.connections {
		result = append(result, conn)
	}

	return result
}

// SaveConnection 保存连接
func (a *App) SaveConnection(connectionData map[string]interface{}) error {
	conn, err := a.mapToConnection(connectionData)
	if err != nil {
		return err
	}

	// 如果没有ID，生成新的ID
	if conn.ID == "" {
		conn.ID = uuid.New().String()
		conn.CreatedAt = time.Now()
	}
	conn.UpdatedAt = time.Now()

	a.mutex.Lock()
	defer a.mutex.Unlock()

	a.connections[conn.ID] = conn
	return nil
}

// DeleteConnection 删除连接
func (a *App) DeleteConnection(connectionID string) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if _, exists := a.connections[connectionID]; !exists {
		return fmt.Errorf("connection not found")
	}

	delete(a.connections, connectionID)
	return nil
}

// TestConnection 测试连接
func (a *App) TestConnection(connectionData map[string]interface{}) error {
	conn, err := a.mapToConnection(connectionData)
	if err != nil {
		return fmt.Errorf("invalid connection data: %w", err)
	}

	// 基本参数验证
	if conn.Host == "" {
		return fmt.Errorf("host is required")
	}
	if conn.Port <= 0 {
		return fmt.Errorf("valid port is required")
	}

	// 对于某些数据库类型，用户名是必需的
	dbType := strings.ToLower(conn.Type)
	if (dbType == "mysql" || dbType == "postgresql" || dbType == "postgres") && conn.Username == "" {
		return fmt.Errorf("username is required for %s", conn.Type)
	}

	log.Printf("Testing connection to %s:%d (type: %s)", conn.Host, conn.Port, conn.Type)

	// 根据数据库类型进行实际连接测试
	switch strings.ToLower(conn.Type) {
	case "mysql":
		return a.testMySQLConnection(conn)
	case "postgresql", "postgres":
		return a.testPostgreSQLConnection(conn)
	case "redis":
		return a.testRedisConnection(conn)
	case "mongodb", "mongo":
		return a.testMongoDBConnection(conn)
	default:
		return fmt.Errorf("unsupported database type: %s", conn.Type)
	}
}

// ExecuteQuery 执行查询
func (a *App) ExecuteQuery(connectionID, query string) (*QueryResult, error) {
	a.mutex.RLock()
	conn, exists := a.connections[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("connection not found")
	}

	log.Printf("Executing query on %s (%s): %s", conn.Name, conn.Type, query)

	// 根据数据库类型执行查询
	switch strings.ToLower(conn.Type) {
	case "redis":
		return a.executeRedisQuery(conn, query)
	case "mysql":
		return a.executeMySQLQuery(conn, query)
	case "postgresql", "postgres":
		return a.executePostgreSQLQuery(conn, query)
	case "mongodb", "mongo":
		return a.executeMongoDBQuery(conn, query)
	default:
		return nil, fmt.Errorf("unsupported database type: %s", conn.Type)
	}
}

// 辅助方法

// mapToConnection 将map转换为Connection
func (a *App) mapToConnection(data map[string]interface{}) (*Connection, error) {
	conn := &Connection{
		Options: make(map[string]string),
	}

	if id, ok := data["id"].(string); ok {
		conn.ID = id
	}

	if name, ok := data["name"].(string); ok {
		conn.Name = name
	} else {
		return nil, fmt.Errorf("name is required")
	}

	if dbType, ok := data["type"].(string); ok {
		conn.Type = dbType
	} else {
		return nil, fmt.Errorf("type is required")
	}

	if host, ok := data["host"].(string); ok {
		conn.Host = host
	} else {
		return nil, fmt.Errorf("host is required")
	}

	if port, ok := data["port"].(float64); ok {
		conn.Port = int(port)
	} else {
		return nil, fmt.Errorf("port is required")
	}

	if username, ok := data["username"].(string); ok {
		conn.Username = username
	}

	if password, ok := data["password"].(string); ok {
		conn.Password = password
	}

	if database, ok := data["database"].(string); ok {
		conn.Database = database
	}

	if ssl, ok := data["ssl"].(bool); ok {
		conn.SSL = ssl
	}

	if options, ok := data["options"].(map[string]interface{}); ok {
		for k, v := range options {
			if str, ok := v.(string); ok {
				conn.Options[k] = str
			}
		}
	}

	return conn, nil
}

// WindowSettings represents window configuration
type WindowSettings struct {
	Width     int  `json:"width"`
	Height    int  `json:"height"`
	X         int  `json:"x"`
	Y         int  `json:"y"`
	Maximized bool `json:"maximized"`
	Minimized bool `json:"minimized"`
}

// GetWindowSettings 获取窗口设置
func (a *App) GetWindowSettings() WindowSettings {
	return WindowSettings{
		Width:     1200,
		Height:    800,
		X:         100,
		Y:         100,
		Maximized: false,
		Minimized: false,
	}
}

// SetWindowSize 设置窗口大小
func (a *App) SetWindowSize(width, height int) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowSetSize(a.ctx, width, height)
	return nil
}

// SetWindowPosition 设置窗口位置
func (a *App) SetWindowPosition(x, y int) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowSetPosition(a.ctx, x, y)
	return nil
}

// MinimizeWindow 最小化窗口
func (a *App) MinimizeWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowMinimise(a.ctx)
	return nil
}

// MaximizeWindow 最大化窗口
func (a *App) MaximizeWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowMaximise(a.ctx)
	return nil
}

// RestoreWindow 恢复窗口
func (a *App) RestoreWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowUnminimise(a.ctx)
	return nil
}

// ToggleMaximize 切换最大化状态
func (a *App) ToggleMaximize() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowToggleMaximise(a.ctx)
	return nil
}

// SetWindowTitle 设置窗口标题
func (a *App) SetWindowTitle(title string) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowSetTitle(a.ctx, title)
	return nil
}

// ShowWindow 显示窗口
func (a *App) ShowWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowShow(a.ctx)
	return nil
}

// HideWindow 隐藏窗口
func (a *App) HideWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowHide(a.ctx)
	return nil
}

// CenterWindow 居中窗口
func (a *App) CenterWindow() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowCenter(a.ctx)
	return nil
}

// SetWindowAlwaysOnTop 设置窗口置顶
func (a *App) SetWindowAlwaysOnTop(alwaysOnTop bool) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	if alwaysOnTop {
		runtime.WindowSetAlwaysOnTop(a.ctx, true)
	} else {
		runtime.WindowSetAlwaysOnTop(a.ctx, false)
	}
	return nil
}

// GetWindowState 获取窗口状态
func (a *App) GetWindowState() map[string]interface{} {
	return map[string]interface{}{
		"isMaximized": false,
		"isMinimized": false,
		"isVisible":   true,
		"isFocused":   true,
	}
}

// SystemTraySettings 系统托盘设置
type SystemTraySettings struct {
	Enabled           bool `json:"enabled"`
	MinimizeToTray    bool `json:"minimizeToTray"`
	CloseToTray       bool `json:"closeToTray"`
	StartMinimized    bool `json:"startMinimized"`
	ShowNotifications bool `json:"showNotifications"`
}

// GetSystemTraySettings 获取系统托盘设置
func (a *App) GetSystemTraySettings() SystemTraySettings {
	return SystemTraySettings{
		Enabled:           true,
		MinimizeToTray:    true,
		CloseToTray:       false,
		StartMinimized:    false,
		ShowNotifications: true,
	}
}

// SetSystemTraySettings 设置系统托盘配置
func (a *App) SetSystemTraySettings(settings SystemTraySettings) error {
	// 这里可以保存设置到配置文件
	log.Printf("System tray settings updated: %+v", settings)
	return nil
}

// ShowNotification 显示系统通知
func (a *App) ShowNotification(title, message string) error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	// 使用Wails的通知功能
	log.Printf("Notification: %s - %s", title, message)
	return nil
}

// MinimizeToTray 最小化到系统托盘
func (a *App) MinimizeToTray() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowHide(a.ctx)
	a.ShowNotification("Database Manager", "应用已最小化到系统托盘")
	return nil
}

// RestoreFromTray 从系统托盘恢复
func (a *App) RestoreFromTray() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	runtime.WindowShow(a.ctx)
	runtime.WindowUnminimise(a.ctx)
	return nil
}

// QuitApplication 退出应用程序
func (a *App) QuitApplication() error {
	if a.ctx == nil {
		return fmt.Errorf("application context not available")
	}

	// 清理资源
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 关闭所有数据库连接
	for id := range a.connections {
		if err := a.DeleteConnection(id); err != nil {
			log.Printf("Error disconnecting from database %s: %v", id, err)
		}
	}

	runtime.Quit(a.ctx)
	return nil
}

// GetTrayMenuItems 获取托盘菜单项
func (a *App) GetTrayMenuItems() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":    "show",
			"label": "显示主窗口",
			"type":  "normal",
		},
		{
			"id":   "separator1",
			"type": "separator",
		},
		{
			"id":    "connections",
			"label": "连接管理",
			"type":  "normal",
		},
		{
			"id":    "settings",
			"label": "设置",
			"type":  "normal",
		},
		{
			"id":   "separator2",
			"type": "separator",
		},
		{
			"id":    "about",
			"label": "关于",
			"type":  "normal",
		},
		{
			"id":    "quit",
			"label": "退出",
			"type":  "normal",
		},
	}
}

// HandleTrayMenuClick 处理托盘菜单点击
func (a *App) HandleTrayMenuClick(menuID string) error {
	switch menuID {
	case "show":
		return a.RestoreFromTray()
	case "connections":
		a.RestoreFromTray()
		// 这里可以触发前端导航到连接管理页面
		return nil
	case "settings":
		a.RestoreFromTray()
		// 这里可以触发前端导航到设置页面
		return nil
	case "about":
		return a.ShowNotification("Database Manager", "版本 1.0.0\n跨平台数据库管理工具")
	case "quit":
		return a.QuitApplication()
	default:
		return fmt.Errorf("unknown menu item: %s", menuID)
	}
}

// FileAssociation 文件关联信息
type FileAssociation struct {
	Extension   string `json:"extension"`
	Description string `json:"description"`
	IconPath    string `json:"iconPath"`
	MimeType    string `json:"mimeType"`
}

// GetSupportedFileTypes 获取支持的文件类型
func (a *App) GetSupportedFileTypes() []FileAssociation {
	return []FileAssociation{
		{
			Extension:   ".sql",
			Description: "SQL Script File",
			IconPath:    "sql-icon",
			MimeType:    "text/sql",
		},
		{
			Extension:   ".dbm",
			Description: "Database Manager Configuration File",
			IconPath:    "config-icon",
			MimeType:    "application/json",
		},
	}
}

// OpenFile 打开文件
func (a *App) OpenFile(filePath string) error {
	if filePath == "" {
		return fmt.Errorf("file path is empty")
	}

	log.Printf("Opening file: %s", filePath)

	// 根据文件扩展名处理不同类型的文件
	switch {
	case strings.HasSuffix(strings.ToLower(filePath), ".sql"):
		return a.openSQLFile(filePath)
	case strings.HasSuffix(strings.ToLower(filePath), ".dbm"):
		return a.openConfigFile(filePath)
	default:
		return fmt.Errorf("unsupported file type: %s", filePath)
	}
}

// openSQLFile 打开SQL文件
func (a *App) openSQLFile(filePath string) error {
	// 这里可以读取SQL文件内容并在编辑器中打开
	log.Printf("Opening SQL file: %s", filePath)

	// 显示主窗口
	if err := a.RestoreFromTray(); err != nil {
		return err
	}

	// 这里可以触发前端打开SQL编辑器并加载文件内容
	return nil
}

// openConfigFile 打开配置文件
func (a *App) openConfigFile(filePath string) error {
	// 这里可以导入配置文件
	log.Printf("Opening config file: %s", filePath)

	// 显示主窗口
	if err := a.RestoreFromTray(); err != nil {
		return err
	}

	// 这里可以触发前端导入配置
	return nil
}

// RegisterFileAssociations 注册文件关联
func (a *App) RegisterFileAssociations() error {
	// 这个方法在安装时调用，用于注册文件关联
	log.Println("Registering file associations...")

	// 在实际实现中，这里会调用平台特定的API来注册文件关联
	// Windows: 修改注册表
	// macOS: 更新Info.plist
	// Linux: 更新.desktop文件和MIME类型

	return nil
}

// UnregisterFileAssociations 取消文件关联
func (a *App) UnregisterFileAssociations() error {
	// 这个方法在卸载时调用，用于清理文件关联
	log.Println("Unregistering file associations...")

	return nil
}

// HandleCommandLineArgs 处理命令行参数
func (a *App) HandleCommandLineArgs(args []string) error {
	for i, arg := range args {
		switch arg {
		case "--file", "-f":
			if i+1 < len(args) {
				return a.OpenFile(args[i+1])
			}
		case "--config", "-c":
			if i+1 < len(args) {
				return a.openConfigFile(args[i+1])
			}
		case "--edit", "-e":
			if i+1 < len(args) {
				return a.openSQLFile(args[i+1])
			}
		case "--new-connection":
			// 打开新建连接对话框
			return a.RestoreFromTray()
		case "--import-config":
			// 打开导入配置对话框
			return a.RestoreFromTray()
		case "--directory", "-d":
			if i+1 < len(args) {
				log.Printf("Opening directory: %s", args[i+1])
				return a.RestoreFromTray()
			}
		}
	}

	return nil
}

// cleanupConnections 清理连接
func (a *App) cleanupConnections() {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	for id, conn := range a.connections {
		if conn.Connected {
			log.Printf("Cleaning up connection: %s", id)
			// 这里可以添加具体的连接清理逻辑
			conn.Connected = false
		}
	}
}

// GetMemoryStats 获取内存统计信息
func (a *App) GetMemoryStats() map[string]interface{} {
	if a.memoryManager == nil {
		return map[string]interface{}{
			"error": "Memory manager not initialized",
		}
	}

	stats := a.memoryManager.GetStats()
	usage := a.memoryManager.GetMemoryUsage()
	gcStats := a.memoryManager.GetGCStats()

	return map[string]interface{}{
		"stats":   stats,
		"usage":   usage,
		"gcStats": gcStats,
	}
}

// ForceGarbageCollection 强制垃圾回收
func (a *App) ForceGarbageCollection() error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	a.memoryManager.ForceGC()
	return nil
}

// SetMemoryThreshold 设置内存阈值
func (a *App) SetMemoryThreshold(thresholdMB int) error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	threshold := uint64(thresholdMB) * 1024 * 1024 // 转换为字节
	a.memoryManager.SetMemoryThreshold(threshold)
	return nil
}

// SetGCInterval 设置GC检查间隔
func (a *App) SetGCInterval(intervalMinutes int) error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	interval := time.Duration(intervalMinutes) * time.Minute
	a.memoryManager.SetGCInterval(interval)
	return nil
}

// OptimizeMemory 优化内存使用
func (a *App) OptimizeMemory() error {
	if a.memoryManager == nil {
		return fmt.Errorf("memory manager not initialized")
	}

	// 清理未使用的连接
	a.cleanupConnections()

	// 强制垃圾回收
	a.memoryManager.ForceGC()

	log.Println("Memory optimization completed")
	return nil
}

// shutdown 应用关闭时的清理
func (a *App) shutdown() {
	log.Println("Shutting down application...")

	// 关闭内存管理器
	if a.memoryManager != nil {
		a.memoryManager.Shutdown()
	}

	// 清理连接
	a.cleanupConnections()

	log.Println("Application shutdown completed")
}

// GetVersion 获取应用版本信息
func (a *App) GetVersion() map[string]interface{} {
	info := version.GetInfo()
	return map[string]interface{}{
		"version":      info.Version,
		"buildTime":    info.BuildTime,
		"gitCommit":    info.GitCommit,
		"gitBranch":    info.GitBranch,
		"goVersion":    info.GoVersion,
		"platform":     info.Platform,
		"architecture": info.Architecture,
		"fullString":   info.String(),
	}
}

// CheckForUpdates 检查应用更新
func (a *App) CheckForUpdates() (map[string]interface{}, error) {
	checker := version.NewUpdateChecker("https://api.github.com/repos/database-manager/database-manager")

	updateInfo, err := checker.CheckForUpdates()
	if err != nil {
		return nil, fmt.Errorf("failed to check for updates: %w", err)
	}

	return map[string]interface{}{
		"available":      updateInfo.Available,
		"latestVersion":  updateInfo.LatestVersion,
		"currentVersion": updateInfo.CurrentVersion,
		"releaseNotes":   updateInfo.ReleaseNotes,
		"downloadUrl":    updateInfo.DownloadURL,
		"releaseDate":    updateInfo.ReleaseDate,
		"critical":       updateInfo.Critical,
	}, nil
}

// GetVersionString 获取版本字符串
func (a *App) GetVersionString() string {
	return version.GetVersionString()
}

// GetFullVersionString 获取完整版本字符串
func (a *App) GetFullVersionString() string {
	return version.GetFullVersionString()
}

// IsDevVersion 判断是否为开发版本
func (a *App) IsDevVersion() bool {
	return version.IsDevVersion()
}

// GetBuildInfo 获取构建信息
func (a *App) GetBuildInfo() map[string]string {
	return map[string]string{
		"version":   version.Version,
		"buildTime": version.BuildTime,
		"gitCommit": version.GitCommit,
		"gitBranch": version.GitBranch,
		"goVersion": version.GoVersion,
	}
}

// testMySQLConnection 测试MySQL连接
func (a *App) testMySQLConnection(conn *Connection) error {
	// 构建MySQL连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=10s",
		conn.Username, conn.Password, conn.Host, conn.Port, conn.Database)

	// 尝试连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}
	defer db.Close()

	// 设置连接超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 测试连接
	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping MySQL server: %w", err)
	}

	log.Printf("MySQL connection test successful: %s:%d", conn.Host, conn.Port)
	return nil
}

// testPostgreSQLConnection 测试PostgreSQL连接
func (a *App) testPostgreSQLConnection(conn *Connection) error {
	// 构建PostgreSQL连接字符串
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable connect_timeout=10",
		conn.Host, conn.Port, conn.Username, conn.Password, conn.Database)

	// 尝试连接
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return fmt.Errorf("failed to open PostgreSQL connection: %w", err)
	}
	defer db.Close()

	// 设置连接超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 测试连接
	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping PostgreSQL server: %w", err)
	}

	log.Printf("PostgreSQL connection test successful: %s:%d", conn.Host, conn.Port)
	return nil
}

// testRedisConnection 测试Redis连接
func (a *App) testRedisConnection(conn *Connection) error {
	// 首先进行TCP连接测试
	addr := fmt.Sprintf("%s:%d", conn.Host, conn.Port)
	timeout := 10 * time.Second

	tcpConn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return fmt.Errorf("failed to connect to Redis server at %s: %w", addr, err)
	}
	tcpConn.Close()

	// 如果TCP连接成功，进行Redis PING测试
	// 构建简单的Redis PING命令
	pingCmd := "*1\r\n$4\r\nPING\r\n"

	redisConn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return fmt.Errorf("failed to connect to Redis for PING test: %w", err)
	}
	defer redisConn.Close()

	// 设置读写超时
	redisConn.SetDeadline(time.Now().Add(timeout))

	// 如果有密码，先进行AUTH
	if conn.Password != "" {
		authCmd := fmt.Sprintf("*2\r\n$4\r\nAUTH\r\n$%d\r\n%s\r\n", len(conn.Password), conn.Password)
		_, err = redisConn.Write([]byte(authCmd))
		if err != nil {
			return fmt.Errorf("failed to send AUTH command: %w", err)
		}

		// 读取AUTH响应
		buffer := make([]byte, 1024)
		n, err := redisConn.Read(buffer)
		if err != nil {
			return fmt.Errorf("failed to read AUTH response: %w", err)
		}

		response := string(buffer[:n])
		if !strings.Contains(response, "+OK") {
			return fmt.Errorf("Redis authentication failed: %s", strings.TrimSpace(response))
		}
	}

	// 发送PING命令
	_, err = redisConn.Write([]byte(pingCmd))
	if err != nil {
		return fmt.Errorf("failed to send PING command: %w", err)
	}

	// 读取PING响应
	buffer := make([]byte, 1024)
	n, err := redisConn.Read(buffer)
	if err != nil {
		return fmt.Errorf("failed to read PING response: %w", err)
	}

	response := string(buffer[:n])
	if !strings.Contains(response, "+PONG") {
		return fmt.Errorf("unexpected Redis PING response: %s", strings.TrimSpace(response))
	}

	log.Printf("Redis connection test successful: %s:%d", conn.Host, conn.Port)
	return nil
}

// testMongoDBConnection 测试MongoDB连接
func (a *App) testMongoDBConnection(conn *Connection) error {
	// 构建MongoDB连接字符串
	var uri string
	if conn.Username != "" && conn.Password != "" {
		uri = fmt.Sprintf("mongodb://%s:%s@%s:%d/%s?connectTimeoutMS=10000&serverSelectionTimeoutMS=10000",
			conn.Username, conn.Password, conn.Host, conn.Port, conn.Database)
	} else {
		uri = fmt.Sprintf("mongodb://%s:%d/%s?connectTimeoutMS=10000&serverSelectionTimeoutMS=10000",
			conn.Host, conn.Port, conn.Database)
	}

	// 设置连接超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 创建MongoDB客户端
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(uri))
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %w", err)
	}
	defer client.Disconnect(ctx)

	// 测试连接
	if err := client.Ping(ctx, nil); err != nil {
		return fmt.Errorf("failed to ping MongoDB server: %w", err)
	}

	log.Printf("MongoDB connection test successful: %s:%d", conn.Host, conn.Port)
	return nil
}

// executeRedisQuery 执行Redis查询
func (a *App) executeRedisQuery(conn *Connection, query string) (*QueryResult, error) {
	addr := fmt.Sprintf("%s:%d", conn.Host, conn.Port)
	timeout := 10 * time.Second

	redisConn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}
	defer redisConn.Close()

	redisConn.SetDeadline(time.Now().Add(timeout))

	// 如果有密码，先进行AUTH
	if conn.Password != "" {
		authCmd := fmt.Sprintf("*2\r\n$4\r\nAUTH\r\n$%d\r\n%s\r\n", len(conn.Password), conn.Password)
		_, err = redisConn.Write([]byte(authCmd))
		if err != nil {
			return nil, fmt.Errorf("failed to send AUTH command: %w", err)
		}

		buffer := make([]byte, 1024)
		n, err := redisConn.Read(buffer)
		if err != nil {
			return nil, fmt.Errorf("failed to read AUTH response: %w", err)
		}

		response := string(buffer[:n])
		if !strings.Contains(response, "+OK") {
			return nil, fmt.Errorf("Redis authentication failed")
		}
	}

	// 解析并执行Redis命令
	parts := strings.Fields(query)
	if len(parts) == 0 {
		return nil, fmt.Errorf("empty command")
	}

	// 构建Redis协议命令
	var cmd strings.Builder
	cmd.WriteString(fmt.Sprintf("*%d\r\n", len(parts)))
	for _, part := range parts {
		cmd.WriteString(fmt.Sprintf("$%d\r\n%s\r\n", len(part), part))
	}

	// 发送命令
	_, err = redisConn.Write([]byte(cmd.String()))
	if err != nil {
		return nil, fmt.Errorf("failed to send command: %w", err)
	}

	// 读取响应
	buffer := make([]byte, 4096)
	n, err := redisConn.Read(buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	response := string(buffer[:n])

	// 解析Redis响应
	return a.parseRedisResponse(response, strings.ToUpper(parts[0]))
}

// parseRedisResponse 解析Redis响应
func (a *App) parseRedisResponse(response, command string) (*QueryResult, error) {
	if strings.HasPrefix(response, "-") {
		// 错误响应
		return &QueryResult{
			Columns: []string{"Error"},
			Rows:    [][]any{{strings.TrimSpace(response[1:])}},
			Count:   0,
			Error:   strings.TrimSpace(response[1:]),
		}, nil
	}

	if strings.HasPrefix(response, "+") {
		// 简单字符串响应
		value := strings.TrimSpace(response[1:])
		return &QueryResult{
			Columns: []string{"Result"},
			Rows:    [][]any{{value}},
			Count:   1,
		}, nil
	}

	if strings.HasPrefix(response, "$") {
		// 批量字符串响应
		lines := strings.Split(response, "\r\n")
		if len(lines) >= 2 {
			value := lines[1]
			if command == "GET" {
				return &QueryResult{
					Columns: []string{"Value"},
					Rows:    [][]any{{value}},
					Count:   1,
				}, nil
			}
			return &QueryResult{
				Columns: []string{"Result"},
				Rows:    [][]any{{value}},
				Count:   1,
			}, nil
		}
	}

	if strings.HasPrefix(response, "*") {
		// 数组响应 (如 KEYS 命令)
		lines := strings.Split(response, "\r\n")
		if len(lines) >= 1 {
			// 解析数组长度
			var rows [][]any
			i := 1
			for i < len(lines) {
				if strings.HasPrefix(lines[i], "$") && i+1 < len(lines) {
					value := lines[i+1]
					if value != "" {
						rows = append(rows, []any{value})
					}
					i += 2
				} else {
					i++
				}
			}

			return &QueryResult{
				Columns: []string{"Key"},
				Rows:    rows,
				Count:   len(rows),
			}, nil
		}
	}

	// 默认响应
	return &QueryResult{
		Columns: []string{"Response"},
		Rows:    [][]any{{strings.TrimSpace(response)}},
		Count:   1,
	}, nil
}
