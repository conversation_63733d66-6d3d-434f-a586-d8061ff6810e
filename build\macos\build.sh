#!/bin/bash
# Database Manager macOS Build Script
# 构建macOS平台的.app包和DMG安装程序

set -e  # 遇到错误时退出

echo "========================================"
echo "Database Manager macOS Build Script"
echo "========================================"

# 设置变量
APP_NAME="Database Manager"
BUNDLE_ID="com.databasemanager.app"
VERSION="1.0.0"
BUILD_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$BUILD_DIR/../.."
DIST_DIR="$BUILD_DIR/dist"
TEMP_DIR="$BUILD_DIR/temp"
APP_DIR="$TEMP_DIR/$APP_NAME.app"

# 检查必要工具
echo "Checking required tools..."

# 检查Go
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed or not in PATH"
    exit 1
fi

# 检查Wails
if ! command -v wails &> /dev/null; then
    echo "Error: Wails is not installed or not in PATH"
    echo "Please install Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed or not in PATH"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

echo "All required tools are available."

# 创建构建目录
echo "Creating build directories..."
rm -rf "$DIST_DIR" "$TEMP_DIR"
mkdir -p "$DIST_DIR" "$TEMP_DIR"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 安装前端依赖
echo "Installing frontend dependencies..."
cd frontend
npm install
cd ..

# 构建前端
echo "Building frontend..."
cd frontend
npm run build
cd ..

# 安装Go依赖
echo "Installing Go dependencies..."
go mod tidy

# 构建应用程序
echo "Building application..."
wails build -platform darwin/amd64 -clean

# 创建.app包结构
echo "Creating .app bundle..."
mkdir -p "$APP_DIR/Contents/MacOS"
mkdir -p "$APP_DIR/Contents/Resources"

# 复制可执行文件
cp "build/bin/database-manager" "$APP_DIR/Contents/MacOS/"

# 复制Info.plist
cp "$BUILD_DIR/Info.plist" "$APP_DIR/Contents/"

# 复制图标文件
if [ -f "$BUILD_DIR/icon.icns" ]; then
    cp "$BUILD_DIR/icon.icns" "$APP_DIR/Contents/Resources/"
else
    echo "Warning: icon.icns not found, using default icon"
fi

# 复制资源文件
if [ -d "resources" ]; then
    cp -r resources "$APP_DIR/Contents/Resources/"
fi

# 复制许可证和说明文件
if [ -f "LICENSE" ]; then
    cp LICENSE "$APP_DIR/Contents/Resources/License.txt"
fi

if [ -f "README.md" ]; then
    cp README.md "$APP_DIR/Contents/Resources/"
fi

# 设置可执行权限
chmod +x "$APP_DIR/Contents/MacOS/database-manager"

# 检查代码签名工具
echo "Checking for code signing..."
SIGN_IDENTITY=""
if command -v codesign &> /dev/null; then
    # 查找可用的签名身份
    AVAILABLE_IDENTITIES=$(security find-identity -v -p codesigning | grep "Developer ID Application" | head -1 | cut -d'"' -f2)
    if [ -n "$AVAILABLE_IDENTITIES" ]; then
        SIGN_IDENTITY="$AVAILABLE_IDENTITIES"
        echo "Found signing identity: $SIGN_IDENTITY"
    else
        echo "Warning: No valid signing identity found"
        echo "The app will not be signed and may show security warnings"
    fi
else
    echo "Warning: codesign not available"
fi

# 代码签名（如果有签名身份）
if [ -n "$SIGN_IDENTITY" ]; then
    echo "Signing application..."
    codesign --force --deep --sign "$SIGN_IDENTITY" "$APP_DIR"
    
    # 验证签名
    echo "Verifying signature..."
    codesign --verify --deep --strict "$APP_DIR"
    if [ $? -eq 0 ]; then
        echo "Application signed successfully"
    else
        echo "Warning: Signature verification failed"
    fi
else
    echo "Skipping code signing"
fi

# 复制.app到分发目录
cp -r "$APP_DIR" "$DIST_DIR/"

# 创建DMG安装程序
echo "Creating DMG installer..."
DMG_NAME="$APP_NAME-$VERSION-macOS"
DMG_PATH="$DIST_DIR/$DMG_NAME.dmg"

# 创建临时DMG目录
DMG_TEMP="$TEMP_DIR/dmg"
mkdir -p "$DMG_TEMP"

# 复制.app到DMG目录
cp -r "$APP_DIR" "$DMG_TEMP/"

# 创建Applications链接
ln -s /Applications "$DMG_TEMP/Applications"

# 复制背景图片和设置文件（如果存在）
if [ -f "$BUILD_DIR/dmg-background.png" ]; then
    mkdir -p "$DMG_TEMP/.background"
    cp "$BUILD_DIR/dmg-background.png" "$DMG_TEMP/.background/"
fi

# 创建DMG
if command -v hdiutil &> /dev/null; then
    echo "Creating DMG with hdiutil..."
    hdiutil create -volname "$APP_NAME" -srcfolder "$DMG_TEMP" -ov -format UDZO "$DMG_PATH"
    
    # 如果有签名身份，签名DMG
    if [ -n "$SIGN_IDENTITY" ]; then
        echo "Signing DMG..."
        codesign --sign "$SIGN_IDENTITY" "$DMG_PATH"
    fi
else
    echo "Warning: hdiutil not available, creating zip archive instead"
    cd "$DMG_TEMP"
    zip -r "$DIST_DIR/$DMG_NAME.zip" .
    cd "$PROJECT_ROOT"
fi

# 创建便携版压缩包
echo "Creating portable archive..."
cd "$DIST_DIR"
zip -r "$APP_NAME-portable-macOS.zip" "$APP_NAME.app"
cd "$PROJECT_ROOT"

# 清理临时文件
echo "Cleaning up..."
rm -rf "$TEMP_DIR"

# 显示构建结果
echo "========================================"
echo "Build completed successfully!"
echo "========================================"
echo "Build artifacts:"
echo "- App Bundle: $DIST_DIR/$APP_NAME.app"
if [ -f "$DMG_PATH" ]; then
    echo "- DMG Installer: $DMG_PATH"
elif [ -f "$DIST_DIR/$DMG_NAME.zip" ]; then
    echo "- Archive: $DIST_DIR/$DMG_NAME.zip"
fi
echo "- Portable: $DIST_DIR/$APP_NAME-portable-macOS.zip"
echo "========================================"

# 询问是否运行应用程序
read -p "Do you want to run the application? (y/n): " run_app
if [[ $run_app =~ ^[Yy]$ ]]; then
    open "$DIST_DIR/$APP_NAME.app"
fi

echo "Build script completed."
