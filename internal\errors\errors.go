package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误代码类型
type ErrorCode string

// 错误代码常量
const (
	// 连接相关错误
	ErrConnectionFailed   ErrorCode = "CONNECTION_FAILED"
	ErrConnectionTimeout  ErrorCode = "CONNECTION_TIMEOUT"
	ErrConnectionClosed   ErrorCode = "CONNECTION_CLOSED"
	ErrInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrConnectionNotFound ErrorCode = "CONNECTION_NOT_FOUND"

	// 查询相关错误
	ErrQueryFailed   ErrorCode = "QUERY_FAILED"
	ErrQueryTimeout  ErrorCode = "QUERY_TIMEOUT"
	ErrInvalidQuery  ErrorCode = "INVALID_QUERY"
	ErrQueryTooLarge ErrorCode = "QUERY_TOO_LARGE"

	// 数据相关错误
	ErrDataNotFound      ErrorCode = "DATA_NOT_FOUND"
	ErrDataCorrupted     ErrorCode = "DATA_CORRUPTED"
	ErrDataTooLarge      ErrorCode = "DATA_TOO_LARGE"
	ErrInvalidDataFormat ErrorCode = "INVALID_DATA_FORMAT"

	// 配置相关错误
	ErrConfigNotFound ErrorCode = "CONFIG_NOT_FOUND"
	ErrConfigInvalid  ErrorCode = "CONFIG_INVALID"
	ErrConfigReadOnly ErrorCode = "CONFIG_READ_ONLY"

	// 权限相关错误
	ErrPermissionDenied ErrorCode = "PERMISSION_DENIED"
	ErrUnauthorized     ErrorCode = "UNAUTHORIZED"

	// 系统相关错误
	ErrInternalError      ErrorCode = "INTERNAL_ERROR"
	ErrServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrRateLimitExceeded  ErrorCode = "RATE_LIMIT_EXCEEDED"

	// 验证相关错误
	ErrValidationFailed ErrorCode = "VALIDATION_FAILED"
	ErrRequiredField    ErrorCode = "REQUIRED_FIELD"
	ErrInvalidFormat    ErrorCode = "INVALID_FORMAT"
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	HTTPStatus int       `json:"-"`
	Cause      error     `json:"-"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 支持错误链
func (e *AppError) Unwrap() error {
	return e.Cause
}

// New 创建新的应用错误
func New(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
	}
}

// Newf 创建带格式化消息的应用错误
func Newf(code ErrorCode, format string, args ...interface{}) *AppError {
	return &AppError{
		Code:       code,
		Message:    fmt.Sprintf(format, args...),
		HTTPStatus: getHTTPStatus(code),
	}
}

// Wrap 包装现有错误
func Wrap(err error, code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
		Cause:      err,
	}
}

// WithDetails 添加详细信息
func (e *AppError) WithDetails(details string) *AppError {
	e.Details = details
	return e
}

// WithCause 添加原因错误
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// getHTTPStatus 根据错误代码获取HTTP状态码
func getHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrConnectionNotFound, ErrDataNotFound, ErrConfigNotFound:
		return http.StatusNotFound
	case ErrInvalidCredentials, ErrUnauthorized:
		return http.StatusUnauthorized
	case ErrPermissionDenied:
		return http.StatusForbidden
	case ErrValidationFailed, ErrRequiredField, ErrInvalidFormat, ErrInvalidQuery, ErrInvalidDataFormat:
		return http.StatusBadRequest
	case ErrQueryTimeout, ErrConnectionTimeout:
		return http.StatusRequestTimeout
	case ErrQueryTooLarge, ErrDataTooLarge:
		return http.StatusRequestEntityTooLarge
	case ErrRateLimitExceeded:
		return http.StatusTooManyRequests
	case ErrServiceUnavailable, ErrConnectionFailed:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}

// IsConnectionError 检查是否为连接相关错误
func IsConnectionError(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		switch appErr.Code {
		case ErrConnectionFailed, ErrConnectionTimeout, ErrConnectionClosed, ErrInvalidCredentials:
			return true
		}
	}
	return false
}

// IsQueryError 检查是否为查询相关错误
func IsQueryError(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		switch appErr.Code {
		case ErrQueryFailed, ErrQueryTimeout, ErrInvalidQuery, ErrQueryTooLarge:
			return true
		}
	}
	return false
}

// IsValidationError 检查是否为验证相关错误
func IsValidationError(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		switch appErr.Code {
		case ErrValidationFailed, ErrRequiredField, ErrInvalidFormat:
			return true
		}
	}
	return false
}

// GetUserFriendlyMessage 获取用户友好的错误消息
func GetUserFriendlyMessage(err error) string {
	if appErr, ok := err.(*AppError); ok {
		switch appErr.Code {
		case ErrConnectionFailed:
			return "无法连接到数据库。请检查连接配置和网络连接。"
		case ErrConnectionTimeout:
			return "连接数据库超时。请检查网络连接或增加超时时间。"
		case ErrInvalidCredentials:
			return "用户名或密码错误。请检查登录凭据。"
		case ErrQueryFailed:
			return "查询执行失败。请检查SQL语法和数据库状态。"
		case ErrQueryTimeout:
			return "查询执行超时。请优化查询或增加超时时间。"
		case ErrInvalidQuery:
			return "SQL语法错误。请检查查询语句。"
		case ErrDataNotFound:
			return "未找到请求的数据。"
		case ErrPermissionDenied:
			return "权限不足。请联系管理员获取相应权限。"
		case ErrConfigInvalid:
			return "配置文件格式错误。请检查配置文件。"
		case ErrServiceUnavailable:
			return "服务暂时不可用。请稍后重试。"
		default:
			return "操作失败。请稍后重试或联系技术支持。"
		}
	}
	return "发生未知错误。请稍后重试。"
}
