{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "test": "Test", "connect": "Connect", "disconnect": "Disconnect", "refresh": "Refresh", "export": "Export", "import": "Import", "search": "Search", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "clear": "Clear", "reset": "Reset"}, "app": {"title": "Database Manager", "description": "A modern database management tool"}, "navigation": {"connections": "Connections", "dataBrowser": "Data Browser", "sqlEditor": "SQL Editor", "settings": "Settings"}, "connection": {"title": "Database Connections", "addConnection": "Add Connection", "editConnection": "Edit Connection", "deleteConnection": "Delete Connection", "testConnection": "Test Connection", "connectionName": "Connection Name", "databaseType": "Database Type", "host": "Host", "port": "Port", "username": "Username", "password": "Password", "database": "Database", "ssl": "Enable SSL", "connected": "Connected", "disconnected": "Disconnected", "connectionTest": {"success": "Connection test successful!", "failed": "Connection test failed!", "testing": "Testing connection..."}, "validation": {"nameRequired": "Connection name is required", "typeRequired": "Database type is required", "hostRequired": "Host is required", "portRequired": "Valid port is required", "databaseRequired": "Database name is required"}, "deleteConfirm": "Are you sure you want to delete \"{name}\"?", "noConnections": "No connections found. Click 'Add Connection' to create one."}, "dataBrowser": {"title": "Data Browser", "noTableSelected": "No table selected", "selectTablePrompt": "Select a table from the sidebar to view its data", "noData": "No data found", "showingEntries": "Showing {start} to {end} of {total} entries"}, "sqlEditor": {"title": "SQL Editor", "placeholder": "Enter your SQL query here...", "execute": "Execute", "executeShortcut": "Execute (Ctrl+Enter)", "noResults": "No results yet", "executePrompt": "Execute a query to see results here", "result": "Result", "rowsAffected": "{count} rows affected", "executionTime": "Execution time: {time}ms", "queryError": "Query Error", "samples": {"selectUsers": "Select Users", "countRecords": "Count Records", "joinTables": "Join Tables", "groupBy": "Group By"}}, "theme": {"light": "Light", "dark": "Dark", "system": "System", "switchTo": "Switch to {theme} theme", "currentTheme": "Current theme: {theme}"}, "language": {"english": "English", "chinese": "中文", "switchTo": "Switch to {language}"}, "errors": {"connectionFailed": "Failed to connect to database", "queryFailed": "Query execution failed", "saveFailed": "Failed to save", "loadFailed": "Failed to load", "networkError": "Network error", "unknownError": "Unknown error occurred"}, "messages": {"connectionSaved": "Connection saved successfully", "connectionDeleted": "Connection deleted successfully", "queryExecuted": "Query executed successfully", "dataExported": "Data exported successfully"}, "settings": {"title": "Settings", "export": "Export", "import": "Import", "reset": "Reset", "importTitle": "Import Settings", "importPlaceholder": "Paste your settings JSON here...", "tabs": {"appearance": "Appearance", "editor": "Editor", "database": "Database", "general": "General"}, "appearance": {"title": "Appearance Settings", "theme": "Theme", "language": "Language", "fontSize": "Font Size", "sidebarWidth": "<PERSON>bar Width"}, "editor": {"title": "Editor Settings", "showLineNumbers": "Show Line Numbers", "autoComplete": "Auto Complete", "wordWrap": "Word Wrap", "tabSize": "<PERSON><PERSON>"}, "database": {"title": "Database Settings", "queryTimeout": "Query Timeout", "maxRows": "<PERSON>s", "autoRefresh": "Auto Refresh", "refreshInterval": "Refresh Interval"}, "general": {"title": "General Settings", "confirmDelete": "Confirm Delete", "showWelcome": "Show Welcome", "autoSave": "Auto Save"}}}