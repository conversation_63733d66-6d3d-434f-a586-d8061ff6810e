package repository

import (
	"context"
	"database-manager/backend/internal/model"
)

// DatabaseConnector 数据库连接器接口
type DatabaseConnector interface {
	// Connect 连接数据库
	Connect(ctx context.Context, config *model.ConnectionConfig) error

	// Disconnect 断开连接
	Disconnect(ctx context.Context) error

	// Ping 测试连接
	Ping(ctx context.Context) error

	// IsConnected 检查连接状态
	IsConnected() bool

	// GetDatabaseInfo 获取数据库信息
	GetDatabaseInfo(ctx context.Context) (*model.DatabaseInfo, error)

	// GetTables 获取表列表
	GetTables(ctx context.Context, database string) ([]*model.TableInfo, error)

	// GetColumns 获取表列信息
	GetColumns(ctx context.Context, database, table string) ([]*model.ColumnInfo, error)

	// ExecuteQuery 执行查询
	ExecuteQuery(ctx context.Context, query string, params ...any) (*model.QueryResult, error)

	// ExecuteCommand 执行命令
	ExecuteCommand(ctx context.Context, command string, params ...any) error
}

// RedisConnector Redis连接器接口
type RedisConnector interface {
	DatabaseConnector

	// GetKeys 获取键列表
	GetKeys(ctx context.Context, pattern string, limit int) ([]string, error)

	// GetKeyInfo 获取键信息
	GetKeyInfo(ctx context.Context, key string) (*model.RedisKeyInfo, error)

	// GetValue 获取键值
	GetValue(ctx context.Context, key string) (any, error)

	// SetValue 设置键值
	SetValue(ctx context.Context, key string, value any, ttl int64) error

	// DeleteKey 删除键
	DeleteKey(ctx context.Context, key string) error

	// GetDatabases 获取数据库列表
	GetDatabases(ctx context.Context) ([]int, error)

	// SelectDatabase 选择数据库
	SelectDatabase(ctx context.Context, db int) error
}

// SQLConnector SQL数据库连接器接口
type SQLConnector interface {
	DatabaseConnector

	// BeginTransaction 开始事务
	BeginTransaction(ctx context.Context) error

	// CommitTransaction 提交事务
	CommitTransaction(ctx context.Context) error

	// RollbackTransaction 回滚事务
	RollbackTransaction(ctx context.Context) error

	// GetSchemas 获取模式列表
	GetSchemas(ctx context.Context) ([]string, error)
}

// MongoConnector MongoDB连接器接口
type MongoConnector interface {
	DatabaseConnector

	// GetCollections 获取集合列表
	GetCollections(ctx context.Context, database string) ([]*model.MongoCollectionInfo, error)

	// FindDocuments 查找文档
	FindDocuments(ctx context.Context, database, collection string, filter map[string]any, limit int64) (*model.QueryResult, error)

	// InsertDocument 插入文档
	InsertDocument(ctx context.Context, database, collection string, document map[string]any) error

	// UpdateDocument 更新文档
	UpdateDocument(ctx context.Context, database, collection string, filter, update map[string]any) error

	// DeleteDocument 删除文档
	DeleteDocument(ctx context.Context, database, collection string, filter map[string]any) error
}
