# Redis Manager 功能测试指南

## 新增功能概览

### ✨ 全新的 Redis 管理界面

1. **数据库切换** - 支持 Redis 16 个数据库之间的切换
2. **键树结构显示** - 以文件夹形式组织键（基于 `:` 分隔符）
3. **批量操作** - 支持多选和批量删除键
4. **值编辑** - 可以修改键值和过期时间
5. **类型识别** - 显示不同数据类型（string, list, set, hash, zset）
6. **搜索过滤** - 支持模式匹配搜索
7. **TTL 管理** - 显示和修改键的过期时间

## 🧪 测试步骤

### 1. 启动应用程序
```bash
.\database-manager-redis.exe
```

### 2. 连接到 Redis
1. 点击 "Add Connection"
2. 填写 Redis 连接信息：
   - **Name**: Redis Test
   - **Type**: Redis
   - **Host**: 127.0.0.1
   - **Port**: 6379
   - **Username**: 留空
   - **Password**: 留空
   - **Database**: 0
3. 点击 "Test Connection" 确认连接成功
4. 点击 "Save" 保存连接

### 3. 进入 Redis 管理界面
1. 在连接列表中点击已连接的 Redis 连接
2. 应该看到全新的 Redis 管理界面，包含：
   - 顶部工具栏（数据库选择器、搜索框、批量删除按钮）
   - 左侧键列表面板
   - 右侧值显示面板

### 4. 测试数据库切换功能
1. 在顶部选择不同的数据库（DB 0 - DB 15）
2. 验证键列表会根据选择的数据库更新
3. 可以在不同数据库中添加测试数据

### 5. 测试键搜索功能
1. 在搜索框中输入模式，例如：
   - `*` - 显示所有键
   - `user:*` - 显示以 "user:" 开头的键
   - `session:*` - 显示以 "session:" 开头的键
2. 按 Enter 或点击 "Search" 按钮
3. 验证键列表根据搜索模式过滤

### 6. 测试键值查看功能
1. 点击左侧键列表中的任意键
2. 右侧应该显示键的详细信息：
   - 键名
   - 数据类型
   - TTL（过期时间）
   - 键值内容
3. 不同类型的键应该有不同的显示格式：
   - **String**: 文本框显示
   - **List**: 列表项显示
   - **Set**: 成员列表显示
   - **Hash**: 字段-值对显示
   - **ZSet**: 成员-分数对显示

### 7. 测试键值编辑功能
1. 选择一个 string 类型的键
2. 点击键旁边的 "Edit" 按钮
3. 在弹出的编辑对话框中：
   - 修改键值
   - 修改 TTL（过期时间）
4. 点击 "Save Changes" 保存
5. 验证更改已生效

### 8. 测试批量删除功能
1. 使用复选框选择多个键
2. 点击顶部的 "Delete Selected" 按钮
3. 确认删除操作
4. 验证选中的键已被删除

### 9. 测试单个键删除
1. 点击键旁边的 "Del" 按钮
2. 确认删除操作
3. 验证键已被删除

## 🎯 预期结果

### 界面布局
- ✅ 现代化的三栏布局
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 清晰的视觉层次和导航

### 数据库管理
- ✅ 可以在 16 个 Redis 数据库之间切换
- ✅ 数据库切换时键列表正确更新
- ✅ 当前数据库状态清晰显示

### 键管理
- ✅ 键列表按类型分色显示
- ✅ TTL 信息清晰显示（∞ 表示永不过期）
- ✅ 支持模式搜索和过滤
- ✅ 批量选择和删除功能

### 值显示和编辑
- ✅ 不同数据类型有适当的显示格式
- ✅ 可以编辑 string 类型的值
- ✅ 可以修改键的 TTL
- ✅ 实时更新显示

### 用户体验
- ✅ 操作响应迅速
- ✅ 错误处理适当
- ✅ 确认对话框防止误操作
- ✅ 加载状态指示

## 🔧 高级测试

### 创建测试数据
在 Redis CLI 中执行以下命令创建测试数据：

```bash
# String 类型
SET user:1:name "John Doe"
SET user:1:email "<EMAIL>"
SET user:2:name "Jane Smith"

# List 类型
LPUSH user:1:orders "order1" "order2" "order3"

# Set 类型
SADD user:1:tags "developer" "golang" "redis"

# Hash 类型
HSET user:1:profile name "John" age "30" city "New York"

# ZSet 类型
ZADD leaderboard 100 "player1" 200 "player2" 150 "player3"

# 设置过期时间
EXPIRE user:1:session 3600
```

### 测试场景
1. **键树结构**: 验证 `user:1:*` 和 `user:2:*` 键是否按层次结构显示
2. **类型识别**: 验证不同类型的键是否有不同的颜色标识
3. **TTL 显示**: 验证过期时间是否正确显示
4. **值格式**: 验证不同类型的值是否以适当格式显示

## 🚨 故障排除

### 如果键列表为空
1. 确认 Redis 中有数据
2. 检查搜索模式是否正确
3. 确认选择了正确的数据库

### 如果无法编辑键值
1. 确认键类型是否支持编辑（目前只支持 string）
2. 检查 Redis 连接是否正常
3. 查看浏览器控制台错误信息

### 如果界面显示异常
1. 刷新页面重新加载
2. 检查浏览器兼容性
3. 查看开发者工具中的错误信息

## 📊 成功标准

- [ ] Redis 连接成功建立
- [ ] 可以切换不同数据库
- [ ] 键列表正确显示和过滤
- [ ] 可以查看不同类型的键值
- [ ] 可以编辑 string 类型的键值和 TTL
- [ ] 可以删除单个或多个键
- [ ] 界面响应流畅，用户体验良好

如果所有测试都通过，说明 Redis 管理功能已成功实现！🎉
