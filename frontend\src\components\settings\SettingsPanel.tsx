import React, { useState } from 'react';
import { useSettings } from '../../hooks/useSettings';
import { useTheme } from '../../hooks/useTheme';
import { useI18n } from '../../hooks/useI18n';
import { Button, Input, Modal } from '../common';
import { clsx } from 'clsx';

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ isOpen, onClose }) => {
  const { settings, updateSettings, resetSettings, exportSettings, importSettings } = useSettings();
  const { setTheme } = useTheme();
  const { setLanguage, t } = useI18n();
  const [activeTab, setActiveTab] = useState<'appearance' | 'editor' | 'database' | 'general'>('appearance');
  const [importText, setImportText] = useState('');
  const [showImportModal, setShowImportModal] = useState(false);

  // 处理设置更新
  const handleSettingChange = (key: keyof typeof settings, value: any) => {
    updateSettings({ [key]: value });

    // 同步更新相关Hook状态
    if (key === 'theme') {
      setTheme(value);
    } else if (key === 'language') {
      setLanguage(value);
    }
  };

  // 处理导入设置
  const handleImportSettings = () => {
    if (importSettings(importText)) {
      setShowImportModal(false);
      setImportText('');
      // 显示成功消息
    } else {
      // 显示错误消息
    }
  };

  // 处理导出设置
  const handleExportSettings = () => {
    const settingsJson = exportSettings();
    navigator.clipboard.writeText(settingsJson);
    // 显示成功消息
  };

  const tabs = [
    { id: 'appearance', label: t('settings.tabs.appearance'), icon: '🎨' },
    { id: 'editor', label: t('settings.tabs.editor'), icon: '📝' },
    { id: 'database', label: t('settings.tabs.database'), icon: '🗄️' },
    { id: 'general', label: t('settings.tabs.general'), icon: '⚙️' },
  ];

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={t('settings.title')}
        size="lg"
      >
        <div className="flex h-96">
          {/* 左侧标签页 */}
          <div className="w-48 border-r border-gray-200 dark:border-dark-600">
            <nav className="space-y-1 p-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={clsx(
                    'w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400'
                      : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-dark-700'
                  )}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* 右侧设置内容 */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'appearance' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {t('settings.appearance.title')}
                </h3>

                {/* 主题设置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('settings.appearance.theme')}
                  </label>
                  <select
                    value={settings.theme}
                    onChange={(e) => handleSettingChange('theme', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:border-dark-600 dark:text-gray-100"
                  >
                    <option value="light">{t('theme.light')}</option>
                    <option value="dark">{t('theme.dark')}</option>
                    <option value="system">{t('theme.system')}</option>
                  </select>
                </div>

                {/* 语言设置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('settings.appearance.language')}
                  </label>
                  <select
                    value={settings.language}
                    onChange={(e) => handleSettingChange('language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:border-dark-600 dark:text-gray-100"
                  >
                    <option value="en">{t('language.english')}</option>
                    <option value="zh">{t('language.chinese')}</option>
                  </select>
                </div>

                {/* 字体大小 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('settings.appearance.fontSize')}
                  </label>
                  <input
                    type="range"
                    min="12"
                    max="20"
                    value={settings.fontSize}
                    onChange={(e) => handleSettingChange('fontSize', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {settings.fontSize}px
                  </div>
                </div>

                {/* 侧边栏宽度 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('settings.appearance.sidebarWidth')}
                  </label>
                  <input
                    type="range"
                    min="200"
                    max="400"
                    value={settings.sidebarWidth}
                    onChange={(e) => handleSettingChange('sidebarWidth', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {settings.sidebarWidth}px
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'editor' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {t('settings.editor.title')}
                </h3>

                {/* 显示行号 */}
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('settings.editor.showLineNumbers')}
                  </label>
                  <input
                    type="checkbox"
                    checked={settings.showLineNumbers}
                    onChange={(e) => handleSettingChange('showLineNumbers', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>

                {/* 自动完成 */}
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('settings.editor.autoComplete')}
                  </label>
                  <input
                    type="checkbox"
                    checked={settings.autoComplete}
                    onChange={(e) => handleSettingChange('autoComplete', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>

                {/* 自动换行 */}
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('settings.editor.wordWrap')}
                  </label>
                  <input
                    type="checkbox"
                    checked={settings.wordWrap}
                    onChange={(e) => handleSettingChange('wordWrap', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>

                {/* Tab大小 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('settings.editor.tabSize')}
                  </label>
                  <select
                    value={settings.tabSize}
                    onChange={(e) => handleSettingChange('tabSize', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:border-dark-600 dark:text-gray-100"
                  >
                    <option value={2}>2</option>
                    <option value={4}>4</option>
                    <option value={8}>8</option>
                  </select>
                </div>
              </div>
            )}

            {/* 其他标签页内容... */}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-dark-600">
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExportSettings}
            >
              {t('settings.export')}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowImportModal(true)}
            >
              {t('settings.import')}
            </Button>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="ghost"
              onClick={resetSettings}
            >
              {t('settings.reset')}
            </Button>
            <Button onClick={onClose}>
              {t('common.close')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* 导入设置模态框 */}
      <Modal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        title={t('settings.importTitle')}
      >
        <div className="space-y-4">
          <textarea
            value={importText}
            onChange={(e) => setImportText(e.target.value)}
            placeholder={t('settings.importPlaceholder')}
            className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:border-dark-600 dark:text-gray-100"
          />
          <div className="flex justify-end space-x-2">
            <Button
              variant="ghost"
              onClick={() => setShowImportModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button onClick={handleImportSettings}>
              {t('settings.import')}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SettingsPanel;