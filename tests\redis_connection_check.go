package main

import (
	"fmt"
	"log"
	"net"
	"strings"
	"time"
)

// TestConnection represents a database connection for testing
type TestConnection struct {
	Host     string
	Port     int
	Password string
	Database string
}

// testRedisConnection 测试Redis连接 (复制自app.go的修复版本)
func testRedisConnection(conn *TestConnection) error {
	// 首先进行TCP连接测试
	addr := fmt.Sprintf("%s:%d", conn.Host, conn.Port)
	timeout := 10 * time.Second

	tcpConn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return fmt.Errorf("failed to connect to Redis server at %s: %w", addr, err)
	}
	tcpConn.Close()

	// 如果TCP连接成功，进行Redis PING测试
	// 构建简单的Redis PING命令
	pingCmd := "*1\r\n$4\r\nPING\r\n"

	redisConn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return fmt.Errorf("failed to connect to Redis for PING test: %w", err)
	}
	defer redisConn.Close()

	// 设置读写超时
	redisConn.SetDeadline(time.Now().Add(timeout))

	// 如果有密码，先进行AUTH
	if conn.Password != "" {
		authCmd := fmt.Sprintf("*2\r\n$4\r\nAUTH\r\n$%d\r\n%s\r\n", len(conn.Password), conn.Password)
		_, err = redisConn.Write([]byte(authCmd))
		if err != nil {
			return fmt.Errorf("failed to send AUTH command: %w", err)
		}

		// 读取AUTH响应
		buffer := make([]byte, 1024)
		n, err := redisConn.Read(buffer)
		if err != nil {
			return fmt.Errorf("failed to read AUTH response: %w", err)
		}

		response := string(buffer[:n])
		if !strings.Contains(response, "+OK") {
			return fmt.Errorf("Redis authentication failed: %s", strings.TrimSpace(response))
		}
	}

	// 发送PING命令
	_, err = redisConn.Write([]byte(pingCmd))
	if err != nil {
		return fmt.Errorf("failed to send PING command: %w", err)
	}

	// 读取PING响应
	buffer := make([]byte, 1024)
	n, err := redisConn.Read(buffer)
	if err != nil {
		return fmt.Errorf("failed to read PING response: %w", err)
	}

	response := string(buffer[:n])
	if !strings.Contains(response, "+PONG") {
		return fmt.Errorf("unexpected Redis PING response: %s", strings.TrimSpace(response))
	}

	log.Printf("Redis connection test successful: %s:%d", conn.Host, conn.Port)
	return nil
}

func main() {
	fmt.Println("Testing Redis Connection Fix")
	fmt.Println("=============================")

	// 测试用例1: 127.0.0.1:6379 (无密码)
	fmt.Println("\n1. Testing 127.0.0.1:6379 (no password)")
	conn1 := &TestConnection{
		Host:     "127.0.0.1",
		Port:     6379,
		Password: "",
		Database: "0",
	}

	err := testRedisConnection(conn1)
	if err != nil {
		fmt.Printf("❌ Test failed: %v\n", err)
		fmt.Println("   This is expected if Redis is not running locally")
	} else {
		fmt.Println("✅ Test passed: Redis connection successful")
	}

	// 测试用例2: localhost:6379 (无密码)
	fmt.Println("\n2. Testing localhost:6379 (no password)")
	conn2 := &TestConnection{
		Host:     "localhost",
		Port:     6379,
		Password: "",
		Database: "0",
	}

	err = testRedisConnection(conn2)
	if err != nil {
		fmt.Printf("❌ Test failed: %v\n", err)
		fmt.Println("   This is expected if Redis is not running locally")
	} else {
		fmt.Println("✅ Test passed: Redis connection successful")
	}

	fmt.Println("\n=============================")
	fmt.Println("Redis Connection Fix Test Complete")
	fmt.Println("\nNote: If you have Redis running locally on port 6379,")
	fmt.Println("the tests should pass. Otherwise, they will fail")
	fmt.Println("with connection errors, which is expected behavior.")
}
