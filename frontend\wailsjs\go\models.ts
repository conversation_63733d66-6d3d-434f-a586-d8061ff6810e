export namespace main {
	
	export class Connection {
	    id: string;
	    name: string;
	    type: string;
	    host: string;
	    port: number;
	    username: string;
	    password: string;
	    database: string;
	    ssl: boolean;
	    options: Record<string, string>;
	    // Go type: time
	    created_at: any;
	    // Go type: time
	    updated_at: any;
	    connected: boolean;
	
	    static createFrom(source: any = {}) {
	        return new Connection(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.name = source["name"];
	        this.type = source["type"];
	        this.host = source["host"];
	        this.port = source["port"];
	        this.username = source["username"];
	        this.password = source["password"];
	        this.database = source["database"];
	        this.ssl = source["ssl"];
	        this.options = source["options"];
	        this.created_at = this.convertValues(source["created_at"], null);
	        this.updated_at = this.convertValues(source["updated_at"], null);
	        this.connected = source["connected"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class FileAssociation {
	    extension: string;
	    description: string;
	    iconPath: string;
	    mimeType: string;
	
	    static createFrom(source: any = {}) {
	        return new FileAssociation(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.extension = source["extension"];
	        this.description = source["description"];
	        this.iconPath = source["iconPath"];
	        this.mimeType = source["mimeType"];
	    }
	}
	export class QueryResult {
	    columns: string[];
	    rows: any[][];
	    count: number;
	    error?: string;
	
	    static createFrom(source: any = {}) {
	        return new QueryResult(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.columns = source["columns"];
	        this.rows = source["rows"];
	        this.count = source["count"];
	        this.error = source["error"];
	    }
	}
	export class SystemTraySettings {
	    enabled: boolean;
	    minimizeToTray: boolean;
	    closeToTray: boolean;
	    startMinimized: boolean;
	    showNotifications: boolean;
	
	    static createFrom(source: any = {}) {
	        return new SystemTraySettings(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.enabled = source["enabled"];
	        this.minimizeToTray = source["minimizeToTray"];
	        this.closeToTray = source["closeToTray"];
	        this.startMinimized = source["startMinimized"];
	        this.showNotifications = source["showNotifications"];
	    }
	}
	export class WindowSettings {
	    width: number;
	    height: number;
	    x: number;
	    y: number;
	    maximized: boolean;
	    minimized: boolean;
	
	    static createFrom(source: any = {}) {
	        return new WindowSettings(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.width = source["width"];
	        this.height = source["height"];
	        this.x = source["x"];
	        this.y = source["y"];
	        this.maximized = source["maximized"];
	        this.minimized = source["minimized"];
	    }
	}

}

