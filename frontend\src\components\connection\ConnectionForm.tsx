import React, { useState, useEffect } from 'react';
import { Button, Input } from '../common';

interface Connection {
  id?: string;
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  options?: Record<string, string>;
}

interface ConnectionFormProps {
  connection?: Connection | null;
  onSave: () => void;
  onCancel: () => void;
}

const ConnectionForm: React.FC<ConnectionFormProps> = ({
  connection,
  onSave,
  onCancel,
}) => {
  const [formData, setFormData] = useState<Connection>({
    name: '',
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: '',
    password: '',
    database: '',
    ssl: false,
    options: {},
  });
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 数据库类型配置
  const databaseTypes = [
    { value: 'mysql', label: 'MySQL', defaultPort: 3306 },
    { value: 'postgresql', label: 'PostgreSQL', defaultPort: 5432 },
    { value: 'redis', label: 'Redis', defaultPort: 6379 },
    { value: 'mongodb', label: 'MongoDB', defaultPort: 27017 },
  ];

  // 初始化表单数据
  useEffect(() => {
    if (connection) {
      setFormData(connection);
    }
  }, [connection]);

  // 处理数据库类型变化
  const handleTypeChange = (type: string) => {
    const dbType = databaseTypes.find(t => t.value === type);
    setFormData(prev => ({
      ...prev,
      type,
      port: dbType?.defaultPort || prev.port,
    }));
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Connection name is required';
    }

    if (!formData.host.trim()) {
      newErrors.host = 'Host is required';
    }

    if (!formData.port || formData.port <= 0 || formData.port > 65535) {
      newErrors.port = 'Valid port number is required (1-65535)';
    }

    if (formData.type !== 'redis' && !formData.database.trim()) {
      newErrors.database = 'Database name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 测试连接
  const handleTest = async () => {
    if (!validateForm()) return;

    setTesting(true);
    try {
      // TODO: 调用API测试连接
      // const response = await fetch('/api/connections/test', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(formData),
      // });

      // 模拟测试
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Connection test successful!');
    } catch (error) {
      console.error('Connection test failed:', error);
      alert('Connection test failed!');
    } finally {
      setTesting(false);
    }
  };

  // 保存连接
  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // TODO: 调用API保存连接
      // const url = connection ? '/api/connections' : '/api/connections';
      // const method = connection ? 'PUT' : 'POST';
      // const response = await fetch(url, {
      //   method,
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(formData),
      // });

      onSave();
    } catch (error) {
      console.error('Failed to save connection:', error);
      alert('Failed to save connection');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 连接名称 */}
        <div className="md:col-span-2">
          <Input
            label="Connection Name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            error={errors.name}
            placeholder="My Database Connection"
          />
        </div>

        {/* 数据库类型 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Database Type
          </label>
          <select
            value={formData.type}
            onChange={(e) => handleTypeChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            {databaseTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* SSL */}
        <div className="flex items-center">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.ssl}
              onChange={(e) => setFormData(prev => ({ ...prev, ssl: e.target.checked }))}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable SSL</span>
          </label>
        </div>

        {/* 主机地址 */}
        <div>
          <Input
            label="Host"
            value={formData.host}
            onChange={(e) => setFormData(prev => ({ ...prev, host: e.target.value }))}
            error={errors.host}
            placeholder="localhost"
          />
        </div>

        {/* 端口 */}
        <div>
          <Input
            label="Port"
            type="number"
            value={formData.port.toString()}
            onChange={(e) => setFormData(prev => ({ ...prev, port: parseInt(e.target.value) || 0 }))}
            error={errors.port}
            placeholder="3306"
          />
        </div>

        {/* 用户名 */}
        <div>
          <Input
            label="Username"
            value={formData.username}
            onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
            placeholder="root"
          />
        </div>

        {/* 密码 */}
        <div>
          <Input
            label="Password"
            type="password"
            value={formData.password}
            onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
            placeholder="••••••••"
          />
        </div>

        {/* 数据库名称 */}
        {formData.type !== 'redis' && (
          <div className="md:col-span-2">
            <Input
              label="Database"
              value={formData.database}
              onChange={(e) => setFormData(prev => ({ ...prev, database: e.target.value }))}
              error={errors.database}
              placeholder="my_database"
              helperText={formData.type === 'mongodb' ? 'MongoDB database name' : 'Database name to connect to'}
            />
          </div>
        )}

        {/* Redis数据库编号 */}
        {formData.type === 'redis' && (
          <div className="md:col-span-2">
            <Input
              label="Database Number"
              type="number"
              value={formData.database}
              onChange={(e) => setFormData(prev => ({ ...prev, database: e.target.value }))}
              placeholder="0"
              helperText="Redis database number (0-15)"
            />
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-between">
        <Button
          variant="secondary"
          onClick={handleTest}
          loading={testing}
          disabled={loading}
        >
          Test Connection
        </Button>

        <div className="flex space-x-3">
          <Button
            variant="ghost"
            onClick={onCancel}
            disabled={loading || testing}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            loading={loading}
            disabled={testing}
          >
            {connection ? 'Update' : 'Save'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConnectionForm;