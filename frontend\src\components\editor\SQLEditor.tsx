import React, { useState, useRef, useEffect } from 'react';
import { Button, Table } from '../common';
import type { Column } from '../common';

interface Connection {
  id: string;
  name: string;
  type: string;
}

interface QueryResult {
  columns: string[];
  rows: any[][];
  rowCount: number;
  executionTime: number;
  error?: string;
}

interface SQLEditorProps {
  connection: Connection;
}

const SQLEditor: React.FC<SQLEditorProps> = ({ connection }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<QueryResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // SQL关键字高亮
  const sqlKeywords = [
    'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP',
    'ALTER', 'TABLE', 'INDEX', 'VIEW', 'DATABASE', 'SCHEMA', 'FUNCTION',
    'PROCEDURE', 'TRIGGER', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL', 'OUTER',
    'ON', 'AS', 'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN', 'LIKE',
    'ORDER', 'BY', 'GROUP', 'HAVING', 'LIMIT', 'OFFSET', 'UNION', 'ALL',
    'DISTINCT', 'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'NULL', 'TRUE', 'FALSE'
  ];

  // 执行查询
  const executeQuery = async () => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      // TODO: 调用API执行查询
      // const response = await fetch('/api/query/execute', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     connection_id: connection.id,
      //     query: query.trim(),
      //   }),
      // });
      // const data = await response.json();

      // 模拟查询结果
      const mockResult = generateMockResult(query);
      setResults([mockResult]);
      setActiveTab(0);
    } catch (error) {
      console.error('Query execution failed:', error);
      setResults([{
        columns: [],
        rows: [],
        rowCount: 0,
        executionTime: 0,
        error: 'Query execution failed: ' + (error as Error).message,
      }]);
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟查询结果
  const generateMockResult = (sql: string): QueryResult => {
    const startTime = Date.now();

    // 简单的SQL解析来生成不同的模拟结果
    const upperSQL = sql.toUpperCase().trim();

    if (upperSQL.includes('SELECT')) {
      if (upperSQL.includes('USERS')) {
        return {
          columns: ['id', 'name', 'email', 'created_at'],
          rows: [
            [1, 'John Doe', '<EMAIL>', '2024-01-01 10:00:00'],
            [2, 'Jane Smith', '<EMAIL>', '2024-01-02 11:30:00'],
            [3, 'Bob Johnson', '<EMAIL>', '2024-01-03 09:15:00'],
          ],
          rowCount: 3,
          executionTime: Date.now() - startTime,
        };
      } else if (upperSQL.includes('PRODUCTS')) {
        return {
          columns: ['id', 'name', 'price', 'category'],
          rows: [
            [1, 'Laptop', 999.99, 'Electronics'],
            [2, 'Mouse', 29.99, 'Electronics'],
            [3, 'Keyboard', 79.99, 'Electronics'],
          ],
          rowCount: 3,
          executionTime: Date.now() - startTime,
        };
      }
    } else if (upperSQL.includes('INSERT') || upperSQL.includes('UPDATE') || upperSQL.includes('DELETE')) {
      return {
        columns: [],
        rows: [],
        rowCount: 1,
        executionTime: Date.now() - startTime,
      };
    }

    return {
      columns: ['result'],
      rows: [['Query executed successfully']],
      rowCount: 1,
      executionTime: Date.now() - startTime,
    };
  };

  // 处理键盘快捷键
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      executeQuery();
    }
  };

  // 插入示例查询
  const insertSampleQuery = (sampleQuery: string) => {
    setQuery(sampleQuery);
    textareaRef.current?.focus();
  };

  // 示例查询
  const sampleQueries = [
    {
      name: 'Select Users',
      query: 'SELECT * FROM users LIMIT 10;'
    },
    {
      name: 'Count Records',
      query: 'SELECT COUNT(*) as total FROM users;'
    },
    {
      name: 'Join Tables',
      query: `SELECT u.name, p.name as product_name, o.total
FROM users u
JOIN orders o ON u.id = o.user_id
JOIN products p ON o.product_id = p.id
LIMIT 10;`
    },
    {
      name: 'Group By',
      query: `SELECT category, COUNT(*) as count, AVG(price) as avg_price
FROM products
GROUP BY category
ORDER BY count DESC;`
    }
  ];

  // 转换结果为Table组件格式
  const getTableColumns = (result: QueryResult): Column[] => {
    return result.columns.map(col => ({
      key: col,
      title: col,
      render: (value) => {
        if (value === null) return <span className="text-gray-400">NULL</span>;
        if (typeof value === 'boolean') return value ? 'true' : 'false';
        if (typeof value === 'object') return JSON.stringify(value);
        return String(value);
      },
    }));
  };

  const getTableData = (result: QueryResult) => {
    return result.rows.map((row, index) => {
      const record: any = { _index: index };
      result.columns.forEach((col, colIndex) => {
        record[col] = row[colIndex];
      });
      return record;
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* 工具栏 */}
      <div className="p-4 border-b border-gray-200 dark:border-dark-600 bg-white dark:bg-dark-800">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              SQL Editor
            </h2>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {connection.name} ({connection.type.toUpperCase()})
            </span>
          </div>
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setQuery('')}
            >
              Clear
            </Button>
            <Button
              size="sm"
              onClick={executeQuery}
              loading={loading}
              disabled={!query.trim()}
            >
              Execute (Ctrl+Enter)
            </Button>
          </div>
        </div>
      </div>

      {/* 编辑器区域 */}
      <div className="flex-1 flex">
        {/* 左侧 - SQL编辑器 */}
        <div className="w-1/2 flex flex-col border-r border-gray-200 dark:border-dark-600">
          {/* 示例查询 */}
          <div className="p-3 border-b border-gray-200 dark:border-dark-600 bg-gray-50 dark:bg-dark-700">
            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">Quick:</span>
              {sampleQueries.map((sample, index) => (
                <button
                  key={index}
                  onClick={() => insertSampleQuery(sample.query)}
                  className="text-xs px-2 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors"
                >
                  {sample.name}
                </button>
              ))}
            </div>
          </div>

          {/* SQL编辑器 */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter your SQL query here..."
              className="w-full h-full p-4 border-0 resize-none focus:outline-none bg-white dark:bg-dark-800 text-gray-900 dark:text-gray-100 font-mono text-sm"
              style={{ minHeight: '300px' }}
            />

            {/* 行号 */}
            <div className="absolute left-0 top-0 p-4 pointer-events-none">
              <div className="text-gray-400 dark:text-gray-600 font-mono text-sm leading-5">
                {query.split('\n').map((_, index) => (
                  <div key={index}>{index + 1}</div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧 - 查询结果 */}
        <div className="w-1/2 flex flex-col">
          {results.length > 0 ? (
            <>
              {/* 结果标签页 */}
              <div className="border-b border-gray-200 dark:border-dark-600">
                <div className="flex">
                  {results.map((result, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveTab(index)}
                      className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                        activeTab === index
                          ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                          : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                      }`}
                    >
                      Result {index + 1}
                      {result.error && (
                        <span className="ml-1 text-red-500">⚠</span>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* 结果内容 */}
              <div className="flex-1 overflow-hidden">
                {results[activeTab] && (
                  <div className="h-full flex flex-col">
                    {/* 结果信息 */}
                    <div className="p-3 bg-gray-50 dark:bg-dark-700 border-b border-gray-200 dark:border-dark-600">
                      {results[activeTab].error ? (
                        <div className="text-red-600 dark:text-red-400 text-sm">
                          Error: {results[activeTab].error}
                        </div>
                      ) : (
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {results[activeTab].rowCount} rows affected •
                          Execution time: {results[activeTab].executionTime}ms
                        </div>
                      )}
                    </div>

                    {/* 数据表格 */}
                    {!results[activeTab].error && results[activeTab].columns.length > 0 && (
                      <div className="flex-1 overflow-hidden">
                        <Table
                          columns={getTableColumns(results[activeTab])}
                          data={getTableData(results[activeTab])}
                          loading={loading}
                          emptyText="No data returned"
                          className="h-full"
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-dark-900">
              <div className="text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                  No results yet
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Execute a query to see results here
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SQLEditor;