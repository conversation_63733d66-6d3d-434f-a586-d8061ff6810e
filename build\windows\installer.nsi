; Database Manager Windows Installer Script
; 使用 NSIS (Nullsoft Scriptable Install System) 创建安装程序

!define APPNAME "Database Manager"
!define COMPANYNAME "Database Manager Team"
!define DESCRIPTION "Cross-platform database management tool"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "https://github.com/database-manager/database-manager"
!define UPDATEURL "https://github.com/database-manager/database-manager/releases"
!define ABOUTURL "https://github.com/database-manager/database-manager"
!define INSTALLSIZE 50000 ; 估计安装大小 (KB)

RequestExecutionLevel admin ; 需要管理员权限

InstallDir "$PROGRAMFILES\${APPNAME}"

; 安装程序属性
Name "${APPNAME}"
Icon "icon.ico"
OutFile "database-manager-setup.exe"

; 版本信息
VIProductVersion "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey ProductName "${APPNAME}"
VIAddVersionKey Comments "${DESCRIPTION}"
VIAddVersionKey CompanyName "${COMPANYNAME}"
VIAddVersionKey LegalCopyright "${COMPANYNAME}"
VIAddVersionKey FileDescription "${APPNAME} Installer"
VIAddVersionKey FileVersion "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey ProductVersion "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey InternalName "${APPNAME}"
VIAddVersionKey LegalTrademarks "${COMPANYNAME}"
VIAddVersionKey OriginalFilename "database-manager-setup.exe"

; 页面配置
!include MUI2.nsh
!define MUI_ABORTWARNING
!define MUI_ICON "icon.ico"
!define MUI_UNICON "icon.ico"

; 安装页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "license.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; 语言
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "SimpChinese"

; 默认安装段
Section "Database Manager (required)" SecMain
    SectionIn RO ; 必需组件
    
    ; 设置输出路径
    SetOutPath $INSTDIR
    
    ; 复制文件
    File "database-manager.exe"
    File "icon.ico"
    File "license.txt"
    File "README.md"
    
    ; 复制资源文件
    SetOutPath $INSTDIR\resources
    File /r "resources\*"
    
    ; 写入注册表
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$\"$INSTDIR$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayIcon" "$\"$INSTDIR\icon.ico$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoRepair" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" ${INSTALLSIZE}
    
    ; 创建卸载程序
    WriteUninstaller "$INSTDIR\uninstall.exe"
SectionEnd

; 桌面快捷方式
Section "Desktop Shortcut" SecDesktop
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\database-manager.exe" "" "$INSTDIR\icon.ico"
SectionEnd

; 开始菜单快捷方式
Section "Start Menu Shortcuts" SecStartMenu
    CreateDirectory "$SMPROGRAMS\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\database-manager.exe" "" "$INSTDIR\icon.ico"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\Uninstall.lnk" "$INSTDIR\uninstall.exe"
SectionEnd

; 文件关联
Section "File Associations" SecFileAssoc
    ; 注册 .sql 文件关联
    WriteRegStr HKCR ".sql" "" "DatabaseManager.SQLFile"
    WriteRegStr HKCR "DatabaseManager.SQLFile" "" "SQL Script File"
    WriteRegStr HKCR "DatabaseManager.SQLFile\DefaultIcon" "" "$INSTDIR\database-manager.exe,1"
    WriteRegStr HKCR "DatabaseManager.SQLFile\shell\open\command" "" '"$INSTDIR\database-manager.exe" "%1"'
    
    ; 注册 .dbm 文件关联
    WriteRegStr HKCR ".dbm" "" "DatabaseManager.ConfigFile"
    WriteRegStr HKCR "DatabaseManager.ConfigFile" "" "Database Manager Configuration File"
    WriteRegStr HKCR "DatabaseManager.ConfigFile\DefaultIcon" "" "$INSTDIR\database-manager.exe,2"
    WriteRegStr HKCR "DatabaseManager.ConfigFile\shell\open\command" "" '"$INSTDIR\database-manager.exe" --config "%1"'
    
    ; 刷新文件关联
    System::Call 'shell32.dll::SHChangeNotify(i, i, i, i) v (0x08000000, 0, 0, 0)'
SectionEnd

; 自动启动
Section /o "Auto Start" SecAutoStart
    WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "${APPNAME}" "$INSTDIR\database-manager.exe --minimized"
SectionEnd

; 组件描述
LangString DESC_SecMain ${LANG_ENGLISH} "Main application files (required)"
LangString DESC_SecMain ${LANG_SIMPCHINESE} "主程序文件（必需）"
LangString DESC_SecDesktop ${LANG_ENGLISH} "Create a desktop shortcut"
LangString DESC_SecDesktop ${LANG_SIMPCHINESE} "创建桌面快捷方式"
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Create start menu shortcuts"
LangString DESC_SecStartMenu ${LANG_SIMPCHINESE} "创建开始菜单快捷方式"
LangString DESC_SecFileAssoc ${LANG_ENGLISH} "Associate .sql and .dbm files with Database Manager"
LangString DESC_SecFileAssoc ${LANG_SIMPCHINESE} "关联 .sql 和 .dbm 文件到数据库管理器"
LangString DESC_SecAutoStart ${LANG_ENGLISH} "Start Database Manager automatically when Windows starts"
LangString DESC_SecAutoStart ${LANG_SIMPCHINESE} "Windows启动时自动启动数据库管理器"

!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
!insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
!insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
!insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
!insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} $(DESC_SecFileAssoc)
!insertmacro MUI_DESCRIPTION_TEXT ${SecAutoStart} $(DESC_SecAutoStart)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; 卸载段
Section "Uninstall"
    ; 删除文件
    Delete "$INSTDIR\database-manager.exe"
    Delete "$INSTDIR\icon.ico"
    Delete "$INSTDIR\license.txt"
    Delete "$INSTDIR\README.md"
    Delete "$INSTDIR\uninstall.exe"
    
    ; 删除资源文件夹
    RMDir /r "$INSTDIR\resources"
    
    ; 删除安装目录
    RMDir "$INSTDIR"
    
    ; 删除快捷方式
    Delete "$DESKTOP\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\Uninstall.lnk"
    RMDir "$SMPROGRAMS\${APPNAME}"
    
    ; 删除注册表项
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
    DeleteRegKey HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "${APPNAME}"
    
    ; 删除文件关联
    DeleteRegKey HKCR ".sql"
    DeleteRegKey HKCR "DatabaseManager.SQLFile"
    DeleteRegKey HKCR ".dbm"
    DeleteRegKey HKCR "DatabaseManager.ConfigFile"
    
    ; 刷新文件关联
    System::Call 'shell32.dll::SHChangeNotify(i, i, i, i) v (0x08000000, 0, 0, 0)'
SectionEnd
