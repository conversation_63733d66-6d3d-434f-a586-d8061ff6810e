#!/bin/bash
# 构建版本信息脚本
# 用于在构建时设置版本信息

set -e

# 获取版本信息
VERSION=${VERSION:-$(git describe --tags --always --dirty 2>/dev/null || echo "dev")}
BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
GO_VERSION=$(go version | awk '{print $3}')

# 如果没有git标签，使用默认版本
if [[ "$VERSION" == "dev" ]] || [[ "$VERSION" == *"fatal"* ]]; then
    VERSION="1.0.0-dev"
fi

# 移除版本号前缀 'v'
VERSION=${VERSION#v}

echo "Building with version information:"
echo "  Version: $VERSION"
echo "  Build Time: $BUILD_TIME"
echo "  Git Commit: $GIT_COMMIT"
echo "  Git Branch: $GIT_BRANCH"
echo "  Go Version: $GO_VERSION"

# 构建ldflags
LDFLAGS="-X 'database-manager/internal/version.Version=$VERSION'"
LDFLAGS="$LDFLAGS -X 'database-manager/internal/version.BuildTime=$BUILD_TIME'"
LDFLAGS="$LDFLAGS -X 'database-manager/internal/version.GitCommit=$GIT_COMMIT'"
LDFLAGS="$LDFLAGS -X 'database-manager/internal/version.GitBranch=$GIT_BRANCH'"

# 如果是发布版本，添加优化标志
if [[ "$VERSION" != *"dev"* ]] && [[ "$VERSION" != *"alpha"* ]] && [[ "$VERSION" != *"beta"* ]]; then
    LDFLAGS="$LDFLAGS -s -w"  # 去除调试信息和符号表
fi

echo "LDFLAGS: $LDFLAGS"

# 导出变量供其他脚本使用
export BUILD_LDFLAGS="$LDFLAGS"
export BUILD_VERSION="$VERSION"
export BUILD_TIME="$BUILD_TIME"
export GIT_COMMIT="$GIT_COMMIT"
export GIT_BRANCH="$GIT_BRANCH"

# 如果传入了构建命令，执行它
if [ $# -gt 0 ]; then
    echo "Executing: $@"
    exec "$@"
fi
