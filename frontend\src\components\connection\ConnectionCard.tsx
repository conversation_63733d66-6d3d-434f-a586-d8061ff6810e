import React, { useState } from 'react';
import { Button } from '../common';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  connected: boolean;
  lastConnected?: string;
  connectionCount?: number;
  tags?: string[];
  group?: string;
  favorite?: boolean;
  created_at: string;
  updated_at: string;
}

interface ConnectionCardProps {
  connection: Connection;
  viewMode: 'grid' | 'list';
  isConnecting: boolean;
  isTesting: boolean;
  onConnect: () => void;
  onTest: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onToggleFavorite: () => void;
}

const ConnectionCard: React.FC<ConnectionCardProps> = ({
  connection,
  viewMode,
  isConnecting,
  isTesting,
  onConnect,
  onTest,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleFavorite,
}) => {
  const [showActions, setShowActions] = useState(false);

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'mysql': return '🐬';
      case 'postgresql': return '🐘';
      case 'redis': return '🔴';
      case 'mongodb': return '🍃';
      default: return '💾';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'mysql': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'postgresql': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      case 'redis': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'mongodb': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatLastConnected = (lastConnected?: string) => {
    if (!lastConnected) return 'Never';
    const date = new Date(lastConnected);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const handleDelete = () => {
    if (confirm(`Are you sure you want to delete "${connection.name}"?`)) {
      onDelete();
    }
  };

  if (viewMode === 'list') {
    return (
      <div
        className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        <div className="flex items-center space-x-4 flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">{getTypeIcon(connection.type)}</span>
            {connection.favorite && <span className="text-yellow-500">⭐</span>}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {connection.name}
              </h3>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(connection.type)}`}>
                {connection.type.toUpperCase()}
              </span>
              {connection.connected && (
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              )}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {connection.host}:{connection.port} • {connection.database}
            </div>
          </div>

          <div className="text-right text-sm text-gray-500 dark:text-gray-400">
            <div>Last: {formatLastConnected(connection.lastConnected)}</div>
            <div>Used: {connection.connectionCount || 0} times</div>
          </div>
        </div>

        <div className={`flex items-center space-x-2 transition-opacity ${showActions ? 'opacity-100' : 'opacity-0'}`}>
          <Button
            size="sm"
            variant="ghost"
            onClick={onToggleFavorite}
            className="text-yellow-500 hover:text-yellow-600"
          >
            {connection.favorite ? '⭐' : '☆'}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onTest}
            disabled={isTesting}
          >
            {isTesting ? 'Testing...' : 'Test'}
          </Button>
          <Button
            size="sm"
            onClick={onConnect}
            disabled={isConnecting || isTesting}
          >
            {isConnecting ? 'Connecting...' : 'Connect'}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onEdit}
          >
            Edit
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onDuplicate}
          >
            Copy
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleDelete}
            className="text-red-500 hover:text-red-600"
          >
            Delete
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className="relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-lg transition-all duration-200 cursor-pointer group"
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      onClick={onConnect}
    >
      {/* 收藏标识 */}
      {connection.favorite && (
        <div className="absolute top-2 right-2">
          <span className="text-yellow-500">⭐</span>
        </div>
      )}

      {/* 连接状态指示器 */}
      <div className={`absolute top-2 left-2 w-3 h-3 rounded-full ${
        connection.connected ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'
      }`}></div>

      {/* 主要内容 */}
      <div className="pt-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-3xl">{getTypeIcon(connection.type)}</span>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
              {connection.name}
            </h3>
            <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(connection.type)}`}>
              {connection.type.toUpperCase()}
            </span>
          </div>
        </div>

        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center">
            <span className="w-12 text-gray-500">Host:</span>
            <span className="truncate">{connection.host}:{connection.port}</span>
          </div>
          <div className="flex items-center">
            <span className="w-12 text-gray-500">DB:</span>
            <span className="truncate">{connection.database}</span>
          </div>
          {connection.username && (
            <div className="flex items-center">
              <span className="w-12 text-gray-500">User:</span>
              <span className="truncate">{connection.username}</span>
            </div>
          )}
        </div>

        {/* 标签 */}
        {connection.tags && connection.tags.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-1">
            {connection.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded"
              >
                {tag}
              </span>
            ))}
            {connection.tags.length > 3 && (
              <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded">
                +{connection.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* 统计信息 */}
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Last: {formatLastConnected(connection.lastConnected)}</span>
            <span>Used: {connection.connectionCount || 0}x</span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className={`mt-3 flex items-center justify-between transition-opacity ${
          showActions ? 'opacity-100' : 'opacity-0'
        }`}>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onToggleFavorite();
              }}
              className="text-yellow-500 hover:text-yellow-600"
            >
              {connection.favorite ? '⭐' : '☆'}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onTest();
              }}
              disabled={isTesting}
            >
              {isTesting ? '...' : 'Test'}
            </Button>
          </div>

          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}
            >
              Edit
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onDuplicate();
              }}
            >
              Copy
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
              className="text-red-500 hover:text-red-600"
            >
              Del
            </Button>
          </div>
        </div>

        {/* 连接按钮 */}
        <Button
          className="w-full mt-3"
          onClick={(e) => {
            e.stopPropagation();
            onConnect();
          }}
          disabled={isConnecting || isTesting}
        >
          {isConnecting ? 'Connecting...' : connection.connected ? 'Open' : 'Connect'}
        </Button>
      </div>
    </div>
  );
};

export default ConnectionCard;
