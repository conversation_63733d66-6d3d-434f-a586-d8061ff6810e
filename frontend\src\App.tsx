import React, { useState } from 'react';
import ConnectionList from './components/connection/ConnectionList';
import DataBrowser from './components/data/DataBrowser';
import RedisManager from './components/data/RedisManager';
import { Button } from './components/common';
import './App.css';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  connected: boolean;
  created_at: string;
  updated_at: string;
}

function App() {
  const [selectedConnection, setSelectedConnection] = useState<Connection | null>(null);

  const handleConnectionSelect = (connection: Connection) => {
    // 只有已连接的连接才能查看数据
    if (connection.connected) {
      setSelectedConnection(connection);
    } else {
      alert('Please test the connection first to ensure it is working before browsing data.');
    }
  };

  const handleBackToConnections = () => {
    setSelectedConnection(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Database Manager
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                {selectedConnection
                  ? `Browsing: ${selectedConnection.name} (${selectedConnection.type})`
                  : 'Modern database management tool'
                }
              </p>
            </div>
            {selectedConnection && (
              <Button
                variant="outline"
                onClick={handleBackToConnections}
              >
                ← Back to Connections
              </Button>
            )}
          </div>
        </header>

        <main>
          {selectedConnection ? (
            selectedConnection.type === 'redis' ? (
              <RedisManager connection={selectedConnection} />
            ) : (
              <DataBrowser connection={selectedConnection} />
            )
          ) : (
            <ConnectionList onConnectionSelect={handleConnectionSelect} />
          )}
        </main>
      </div>
    </div>
  );
}

export default App;
