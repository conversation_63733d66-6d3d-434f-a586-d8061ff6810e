import React, { useState, useEffect } from 'react';
import { But<PERSON>, Table, Modal, ErrorAlert } from '../common';
import type { Column } from '../common';
import ConnectionForm from './ConnectionForm';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  connected: boolean;
  created_at: string;
  updated_at: string;
}

interface ConnectionListProps {
  onConnectionSelect?: (connection: Connection) => void;
}

const ConnectionList: React.FC<ConnectionListProps> = ({ onConnectionSelect }) => {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingConnection, setEditingConnection] = useState<Connection | null>(null);

  // 测试连接状态管理
  const [testingConnections, setTestingConnections] = useState<Set<string>>(new Set());
  const [testResults, setTestResults] = useState<Map<string, { success: boolean; message: string; timestamp: number }>>(new Map());
  const [showTestResult, setShowTestResult] = useState<{ connectionId: string; result: { success: boolean; message: string } } | null>(null);

  // 获取连接列表
  const fetchConnections = async () => {
    try {
      setLoading(true);
      // TODO: 调用API获取连接列表
      const response = await fetch('/api/connections');
      const data = await response.json();
      setConnections(data.data || []);

      // 模拟数据
      // setConnections([
      //   {
      //     id: '1',
      //     name: 'Local Redis',
      //     type: 'redis',
      //     host: 'localhost',
      //     port: 6379,
      //     username: '',
      //     password: '',
      //     database: '0',
      //     ssl: false,
      //     connected: true,
      //     created_at: '2024-01-01T00:00:00Z',
      //     updated_at: '2024-01-01T00:00:00Z',
      //   },
      //   {
      //     id: '2',
      //     name: 'Production MySQL',
      //     type: 'mysql',
      //     host: 'prod-mysql.example.com',
      //     port: 3306,
      //     username: 'admin',
      //     password: 'password',
      //     database: 'app_db',
      //     ssl: true,
      //     connected: false,
      //     created_at: '2024-01-01T00:00:00Z',
      //     updated_at: '2024-01-01T00:00:00Z',
      //   },
      // ]);
    } catch (error) {
      console.error('Failed to fetch connections:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConnections();
  }, []);

  // 删除连接
  const handleDelete = async (connection: Connection) => {
    if (!confirm(`Are you sure you want to delete "${connection.name}"?`)) {
      return;
    }

    try {
      // TODO: 调用API删除连接
      await fetch(`/api/connections?id=${connection.id}`, { method: 'DELETE' });

      // setConnections(prev => prev.filter(c => c.id !== connection.id));
    } catch (error) {
      console.error('Failed to delete connection:', error);
    }
  };

  // 测试连接
  const handleTest = async (connection: Connection) => {
    const connectionId = connection.id;

    // 设置测试状态
    setTestingConnections(prev => new Set(prev).add(connectionId));

    try {
      // 调用Wails后端API测试连接
      const result = await (window as any).go.main.App.TestConnection({
        id: connection.id,
        name: connection.name,
        type: connection.type,
        host: connection.host,
        port: connection.port,
        username: connection.username,
        password: connection.password,
        database: connection.database,
        ssl: connection.ssl,
      });

      // 测试成功
      const successResult = {
        success: true,
        message: `Successfully connected to ${connection.type} database at ${connection.host}:${connection.port}`,
        timestamp: Date.now(),
      };

      setTestResults(prev => new Map(prev).set(connectionId, successResult));
      setShowTestResult({ connectionId, result: successResult });

    } catch (error) {
      // 测试失败
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const failureResult = {
        success: false,
        message: errorMessage,
        timestamp: Date.now(),
      };

      setTestResults(prev => new Map(prev).set(connectionId, failureResult));
      setShowTestResult({ connectionId, result: failureResult });

      console.error('Connection test failed:', error);
    } finally {
      // 清除测试状态
      setTestingConnections(prev => {
        const newSet = new Set(prev);
        newSet.delete(connectionId);
        return newSet;
      });
    }
  };

  // 编辑连接
  const handleEdit = (connection: Connection) => {
    setEditingConnection(connection);
    setShowForm(true);
  };

  // 连接成功回调
  const handleConnectionSaved = () => {
    setShowForm(false);
    setEditingConnection(null);
    fetchConnections();
  };

  // 获取数据库类型图标
  const getDatabaseIcon = (type: string) => {
    const icons = {
      redis: '🔴',
      mysql: '🐬',
      postgresql: '🐘',
      mongodb: '🍃',
    };
    return icons[type as keyof typeof icons] || '💾';
  };

  // 获取连接状态
  const getConnectionStatus = (connected: boolean) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        connected
          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      }`}>
        <span className={`w-1.5 h-1.5 mr-1.5 rounded-full ${
          connected ? 'bg-green-400' : 'bg-red-400'
        }`} />
        {connected ? 'Connected' : 'Disconnected'}
      </span>
    );
  };

  const columns: Column[] = [
    {
      key: 'name',
      title: 'Name',
      render: (value, record) => (
        <div className="flex items-center">
          <span className="text-lg mr-2">{getDatabaseIcon(record.type)}</span>
          <div>
            <div className="font-medium text-gray-900 dark:text-gray-100">{value}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{record.type.toUpperCase()}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'host',
      title: 'Host',
      render: (value, record) => `${value}:${record.port}`,
    },
    {
      key: 'database',
      title: 'Database',
    },
    {
      key: 'connected',
      title: 'Status',
      align: 'center',
      render: (value) => getConnectionStatus(value),
    },
    {
      key: 'actions',
      title: 'Actions',
      align: 'right',
      render: (_, record) => {
        const isTestingThis = testingConnections.has(record.id);
        const testResult = testResults.get(record.id);
        const isRecentTest = testResult && (Date.now() - testResult.timestamp) < 30000; // 30秒内的测试结果

        return (
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleTest(record)}
                disabled={isTestingThis}
                className={`${
                  isRecentTest
                    ? testResult.success
                      ? 'text-green-600 hover:text-green-700'
                      : 'text-red-600 hover:text-red-700'
                    : ''
                }`}
              >
                {isTestingThis ? (
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    <span>Testing...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1">
                    {isRecentTest && (
                      <span className={`w-2 h-2 rounded-full ${testResult.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                    )}
                    <span>Test</span>
                  </div>
                )}
              </Button>

              {/* 测试结果工具提示 */}
              {isRecentTest && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 rounded opacity-0 hover:opacity-100 transition-opacity pointer-events-none z-10">
                  {testResult.success ? 'Last test: Success' : 'Last test: Failed'}
                </div>
              )}
            </div>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleEdit(record)}
            >
              Edit
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleDelete(record)}
            >
              Delete
            </Button>
            {onConnectionSelect && (
              <Button
                size="sm"
                variant="primary"
                onClick={() => onConnectionSelect(record)}
              >
                Connect
              </Button>
            )}
          </div>
        );
      },
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Database Connections
        </h2>
        <Button onClick={() => setShowForm(true)}>
          Add Connection
        </Button>
      </div>

      <Table
        columns={columns}
        data={connections}
        loading={loading}
        emptyText="No connections found. Click 'Add Connection' to create one."
        onRowClick={onConnectionSelect}
      />

      <Modal
        isOpen={showForm}
        onClose={() => {
          setShowForm(false);
          setEditingConnection(null);
        }}
        title={editingConnection ? 'Edit Connection' : 'Add Connection'}
        size="lg"
      >
        <ConnectionForm
          connection={editingConnection}
          onSave={handleConnectionSaved}
          onCancel={() => {
            setShowForm(false);
            setEditingConnection(null);
          }}
        />
      </Modal>

      {/* 测试结果弹窗 */}
      {showTestResult && (
        <Modal
          isOpen={true}
          onClose={() => setShowTestResult(null)}
          title="Connection Test Result"
          size="md"
        >
          <div className="space-y-4">
            <div className={`flex items-center space-x-3 p-4 rounded-lg ${
              showTestResult.result.success
                ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
            }`}>
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                showTestResult.result.success
                  ? 'bg-green-100 dark:bg-green-800'
                  : 'bg-red-100 dark:bg-red-800'
              }`}>
                {showTestResult.result.success ? (
                  <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                )}
              </div>

              <div className="flex-1">
                <h3 className={`text-sm font-medium ${
                  showTestResult.result.success
                    ? 'text-green-800 dark:text-green-200'
                    : 'text-red-800 dark:text-red-200'
                }`}>
                  {showTestResult.result.success ? 'Connection Successful' : 'Connection Failed'}
                </h3>
                <p className={`text-sm mt-1 ${
                  showTestResult.result.success
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-red-700 dark:text-red-300'
                }`}>
                  {showTestResult.result.message}
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowTestResult(null)}
              >
                Close
              </Button>
              {!showTestResult.result.success && (
                <Button
                  variant="primary"
                  onClick={() => {
                    setShowTestResult(null);
                    const connection = connections.find(c => c.id === showTestResult.connectionId);
                    if (connection) {
                      handleEdit(connection);
                    }
                  }}
                >
                  Edit Connection
                </Button>
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ConnectionList;