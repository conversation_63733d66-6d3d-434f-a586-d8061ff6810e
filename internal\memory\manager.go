package memory

import (
	"context"
	"log"
	"runtime"
	"runtime/debug"
	"sync"
	"time"
)

// Manager 内存管理器
type Manager struct {
	ctx           context.Context
	cancel        context.CancelFunc
	mutex         sync.RWMutex
	gcInterval    time.Duration
	memThreshold  uint64 // 内存阈值，单位：字节
	lastGCTime    time.Time
	stats         *Stats
	cleanupFuncs  []func()
}

// Stats 内存统计信息
type Stats struct {
	Alloc         uint64    `json:"alloc"`         // 当前分配的内存
	TotalAlloc    uint64    `json:"totalAlloc"`    // 总分配的内存
	Sys           uint64    `json:"sys"`           // 系统内存
	NumGC         uint32    `json:"numGC"`         // GC次数
	LastGC        time.Time `json:"lastGC"`        // 最后一次GC时间
	GCCPUFraction float64   `json:"gcCPUFraction"` // GC CPU占用率
}

// NewManager 创建内存管理器
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())
	
	m := &Manager{
		ctx:          ctx,
		cancel:       cancel,
		gcInterval:   5 * time.Minute,  // 默认5分钟检查一次
		memThreshold: 100 * 1024 * 1024, // 默认100MB阈值
		stats:        &Stats{},
		cleanupFuncs: make([]func(), 0),
	}
	
	// 启动内存监控
	go m.monitor()
	
	return m
}

// SetGCInterval 设置GC检查间隔
func (m *Manager) SetGCInterval(interval time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.gcInterval = interval
}

// SetMemoryThreshold 设置内存阈值
func (m *Manager) SetMemoryThreshold(threshold uint64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.memThreshold = threshold
}

// AddCleanupFunc 添加清理函数
func (m *Manager) AddCleanupFunc(fn func()) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.cleanupFuncs = append(m.cleanupFuncs, fn)
}

// GetStats 获取内存统计信息
func (m *Manager) GetStats() *Stats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	var ms runtime.MemStats
	runtime.ReadMemStats(&ms)
	
	stats := &Stats{
		Alloc:         ms.Alloc,
		TotalAlloc:    ms.TotalAlloc,
		Sys:           ms.Sys,
		NumGC:         ms.NumGC,
		GCCPUFraction: ms.GCCPUFraction,
	}
	
	if ms.LastGC > 0 {
		stats.LastGC = time.Unix(0, int64(ms.LastGC))
	}
	
	m.stats = stats
	return stats
}

// ForceGC 强制执行垃圾回收
func (m *Manager) ForceGC() {
	log.Println("Forcing garbage collection...")
	
	// 执行清理函数
	m.runCleanupFuncs()
	
	// 强制GC
	runtime.GC()
	
	// 释放未使用的内存给操作系统
	debug.FreeOSMemory()
	
	m.mutex.Lock()
	m.lastGCTime = time.Now()
	m.mutex.Unlock()
	
	log.Println("Garbage collection completed")
}

// monitor 内存监控循环
func (m *Manager) monitor() {
	ticker := time.NewTicker(m.gcInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkMemoryUsage()
		}
	}
}

// checkMemoryUsage 检查内存使用情况
func (m *Manager) checkMemoryUsage() {
	stats := m.GetStats()
	
	// 如果内存使用超过阈值，执行GC
	if stats.Alloc > m.memThreshold {
		log.Printf("Memory usage (%d bytes) exceeds threshold (%d bytes), triggering GC", 
			stats.Alloc, m.memThreshold)
		m.ForceGC()
	}
	
	// 记录内存统计
	log.Printf("Memory stats - Alloc: %d bytes, Sys: %d bytes, NumGC: %d", 
		stats.Alloc, stats.Sys, stats.NumGC)
}

// runCleanupFuncs 执行清理函数
func (m *Manager) runCleanupFuncs() {
	m.mutex.RLock()
	funcs := make([]func(), len(m.cleanupFuncs))
	copy(funcs, m.cleanupFuncs)
	m.mutex.RUnlock()
	
	for _, fn := range funcs {
		if fn != nil {
			fn()
		}
	}
}

// Shutdown 关闭内存管理器
func (m *Manager) Shutdown() {
	log.Println("Shutting down memory manager...")
	
	// 取消监控
	m.cancel()
	
	// 执行最后的清理
	m.ForceGC()
	
	log.Println("Memory manager shutdown completed")
}

// GetMemoryUsage 获取内存使用情况（MB）
func (m *Manager) GetMemoryUsage() map[string]float64 {
	stats := m.GetStats()
	
	return map[string]float64{
		"allocated": float64(stats.Alloc) / 1024 / 1024,
		"system":    float64(stats.Sys) / 1024 / 1024,
		"total":     float64(stats.TotalAlloc) / 1024 / 1024,
	}
}

// SetGCPercent 设置GC目标百分比
func (m *Manager) SetGCPercent(percent int) int {
	return debug.SetGCPercent(percent)
}

// SetMaxStack 设置最大栈大小
func (m *Manager) SetMaxStack(bytes int) int {
	return debug.SetMaxStack(bytes)
}

// SetMaxThreads 设置最大线程数
func (m *Manager) SetMaxThreads(threads int) int {
	return debug.SetMaxThreads(threads)
}

// GetGCStats 获取详细的GC统计信息
func (m *Manager) GetGCStats() map[string]interface{} {
	var ms runtime.MemStats
	runtime.ReadMemStats(&ms)
	
	return map[string]interface{}{
		"numGC":           ms.NumGC,
		"numForcedGC":     ms.NumForcedGC,
		"gcCPUFraction":   ms.GCCPUFraction,
		"enableGC":        ms.EnableGC,
		"debugGC":         ms.DebugGC,
		"pauseTotalNs":    ms.PauseTotalNs,
		"pauseNs":         ms.PauseNs,
		"pauseEnd":        ms.PauseEnd,
		"nextGC":          ms.NextGC,
		"lastGC":          ms.LastGC,
		"heapAlloc":       ms.HeapAlloc,
		"heapSys":         ms.HeapSys,
		"heapIdle":        ms.HeapIdle,
		"heapInuse":       ms.HeapInuse,
		"heapReleased":    ms.HeapReleased,
		"heapObjects":     ms.HeapObjects,
		"stackInuse":      ms.StackInuse,
		"stackSys":        ms.StackSys,
		"mSpanInuse":      ms.MSpanInuse,
		"mSpanSys":        ms.MSpanSys,
		"mCacheInuse":     ms.MCacheInuse,
		"mCacheSys":       ms.MCacheSys,
		"buckHashSys":     ms.BuckHashSys,
		"gcSys":           ms.GCSys,
		"otherSys":        ms.OtherSys,
	}
}
