<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Database Manager Connection Fix Test</h1>
    
    <div class="test-section">
        <h2>Test Connection Form</h2>
        <form id="connectionForm">
            <label>Name:</label>
            <input type="text" id="name" value="Test Connection" required>
            
            <label>Type:</label>
            <select id="type">
                <option value="mysql">MySQL</option>
                <option value="postgresql">PostgreSQL</option>
                <option value="redis">Redis</option>
                <option value="mongodb">MongoDB</option>
            </select>
            
            <label>Host:</label>
            <input type="text" id="host" value="localhost" required>
            
            <label>Port:</label>
            <input type="number" id="port" value="3306" required>
            
            <label>Username:</label>
            <input type="text" id="username" value="root">
            
            <label>Password:</label>
            <input type="password" id="password" value="">
            
            <label>Database:</label>
            <input type="text" id="database" value="test">
            
            <label>
                <input type="checkbox" id="ssl"> Enable SSL
            </label>
        </form>
        
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="saveConnection()">Save Connection</button>
        <button onclick="getConnections()">Get Connections</button>
        
        <div id="testResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Connection List</h2>
        <div id="connectionList"></div>
        <button onclick="refreshConnections()">Refresh</button>
    </div>

    <script>
        // 检查是否在 Wails 环境中
        function isWailsEnvironment() {
            return window.go && window.go.main && window.go.main.App;
        }

        // 获取表单数据
        function getFormData() {
            return {
                name: document.getElementById('name').value,
                type: document.getElementById('type').value,
                host: document.getElementById('host').value,
                port: parseInt(document.getElementById('port').value),
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                database: document.getElementById('database').value,
                ssl: document.getElementById('ssl').checked,
                options: {}
            };
        }

        // 显示结果
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        // 测试连接
        async function testConnection() {
            if (!isWailsEnvironment()) {
                showResult('testResult', 'Error: Not running in Wails environment', false);
                return;
            }

            const formData = getFormData();
            
            try {
                await window.go.main.App.TestConnection(formData);
                showResult('testResult', 'Connection test successful!', true);
            } catch (error) {
                showResult('testResult', `Connection test failed: ${error.message}`, false);
            }
        }

        // 保存连接
        async function saveConnection() {
            if (!isWailsEnvironment()) {
                showResult('testResult', 'Error: Not running in Wails environment', false);
                return;
            }

            const formData = getFormData();
            
            try {
                await window.go.main.App.SaveConnection(formData);
                showResult('testResult', 'Connection saved successfully!', true);
                refreshConnections();
            } catch (error) {
                showResult('testResult', `Failed to save connection: ${error.message}`, false);
            }
        }

        // 获取连接列表
        async function getConnections() {
            if (!isWailsEnvironment()) {
                showResult('connectionList', 'Error: Not running in Wails environment', false);
                return;
            }

            try {
                const connections = await window.go.main.App.GetConnections();
                displayConnections(connections);
            } catch (error) {
                showResult('connectionList', `Failed to get connections: ${error.message}`, false);
            }
        }

        // 显示连接列表
        function displayConnections(connections) {
            const listElement = document.getElementById('connectionList');
            
            if (!connections || connections.length === 0) {
                listElement.innerHTML = '<p>No connections found.</p>';
                return;
            }

            let html = '<h3>Connections:</h3><ul>';
            connections.forEach(conn => {
                html += `<li>
                    <strong>${conn.name}</strong> (${conn.type}) - ${conn.host}:${conn.port}
                    <button onclick="deleteConnection('${conn.id}')">Delete</button>
                </li>`;
            });
            html += '</ul>';
            
            listElement.innerHTML = html;
        }

        // 删除连接
        async function deleteConnection(connectionId) {
            if (!isWailsEnvironment()) {
                alert('Error: Not running in Wails environment');
                return;
            }

            if (!confirm('Are you sure you want to delete this connection?')) {
                return;
            }

            try {
                await window.go.main.App.DeleteConnection(connectionId);
                refreshConnections();
            } catch (error) {
                alert(`Failed to delete connection: ${error.message}`);
            }
        }

        // 刷新连接列表
        function refreshConnections() {
            getConnections();
        }

        // 页面加载时检查环境
        window.onload = function() {
            if (isWailsEnvironment()) {
                showResult('testResult', 'Wails environment detected. Ready to test!', true);
                refreshConnections();
            } else {
                showResult('testResult', 'Warning: Not running in Wails environment. Please run this in the Database Manager app.', false);
            }
        };
    </script>
</body>
</html>
