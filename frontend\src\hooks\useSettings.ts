import { useState, useEffect } from 'react';
import { Theme } from './useTheme';
import { Language } from './useI18n';

export interface UserSettings {
  // 外观设置
  theme: Theme;
  language: Language;

  // 界面设置
  sidebarWidth: number;
  sidebarCollapsed: boolean;
  showLineNumbers: boolean;
  fontSize: number;

  // 编辑器设置
  autoComplete: boolean;
  wordWrap: boolean;
  tabSize: number;

  // 数据库设置
  queryTimeout: number;
  maxRows: number;
  autoRefresh: boolean;
  refreshInterval: number;

  // 其他设置
  confirmDelete: boolean;
  showWelcome: boolean;
  autoSave: boolean;
}

const DEFAULT_SETTINGS: UserSettings = {
  // 外观设置
  theme: 'system',
  language: 'en',

  // 界面设置
  sidebarWidth: 280,
  sidebarCollapsed: false,
  showLineNumbers: true,
  fontSize: 14,

  // 编辑器设置
  autoComplete: true,
  wordWrap: true,
  tabSize: 2,

  // 数据库设置
  queryTimeout: 30000,
  maxRows: 1000,
  autoRefresh: false,
  refreshInterval: 30000,

  // 其他设置
  confirmDelete: true,
  showWelcome: true,
  autoSave: true,
};

const SETTINGS_STORAGE_KEY = 'database-manager-settings';

interface SettingsConfig {
  settings: UserSettings;
  updateSettings: (updates: Partial<UserSettings>) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (settingsJson: string) => boolean;
}

export const useSettings = (): SettingsConfig => {
  const [settings, setSettings] = useState<UserSettings>(() => {
    try {
      const savedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY);
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        // 合并默认设置和保存的设置，确保新增的设置项有默认值
        return { ...DEFAULT_SETTINGS, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load settings from localStorage:', error);
    }
    return DEFAULT_SETTINGS;
  });

  // 保存设置到localStorage
  const saveSettings = (newSettings: UserSettings) => {
    try {
      localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(newSettings));
    } catch (error) {
      console.error('Failed to save settings to localStorage:', error);
    }
  };

  // 更新设置
  const updateSettings = (updates: Partial<UserSettings>) => {
    const newSettings = { ...settings, ...updates };
    setSettings(newSettings);
    saveSettings(newSettings);
  };

  // 重置设置
  const resetSettings = () => {
    setSettings(DEFAULT_SETTINGS);
    saveSettings(DEFAULT_SETTINGS);
  };

  // 导出设置
  const exportSettings = (): string => {
    return JSON.stringify(settings, null, 2);
  };

  // 导入设置
  const importSettings = (settingsJson: string): boolean => {
    try {
      const importedSettings = JSON.parse(settingsJson);

      // 验证导入的设置是否有效
      if (typeof importedSettings !== 'object' || importedSettings === null) {
        return false;
      }

      // 合并导入的设置和默认设置
      const newSettings = { ...DEFAULT_SETTINGS, ...importedSettings };
      setSettings(newSettings);
      saveSettings(newSettings);
      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  };

  return {
    settings,
    updateSettings,
    resetSettings,
    exportSettings,
    importSettings,
  };
};