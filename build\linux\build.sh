#!/bin/bash
# Database Manager Linux Build Script
# 构建Linux平台的AppImage、deb和rpm包

set -e  # 遇到错误时退出

echo "========================================"
echo "Database Manager Linux Build Script"
echo "========================================"

# 设置变量
APP_NAME="database-manager"
APP_DISPLAY_NAME="Database Manager"
VERSION="1.0.0"
DESCRIPTION="Cross-platform database management tool"
MAINTAINER="Database Manager Team <<EMAIL>>"
BUILD_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$BUILD_DIR/../.."
DIST_DIR="$BUILD_DIR/dist"
TEMP_DIR="$BUILD_DIR/temp"

# 检查必要工具
echo "Checking required tools..."

# 检查Go
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed or not in PATH"
    exit 1
fi

# 检查Wails
if ! command -v wails &> /dev/null; then
    echo "Error: Wails is not installed or not in PATH"
    echo "Please install Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed or not in PATH"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

echo "All required tools are available."

# 创建构建目录
echo "Creating build directories..."
rm -rf "$DIST_DIR" "$TEMP_DIR"
mkdir -p "$DIST_DIR" "$TEMP_DIR"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 安装前端依赖
echo "Installing frontend dependencies..."
cd frontend
npm install
cd ..

# 构建前端
echo "Building frontend..."
cd frontend
npm run build
cd ..

# 安装Go依赖
echo "Installing Go dependencies..."
go mod tidy

# 构建应用程序
echo "Building application..."
wails build -platform linux/amd64 -clean

# 创建基本目录结构
echo "Creating package structure..."
PACKAGE_DIR="$TEMP_DIR/package"
mkdir -p "$PACKAGE_DIR/usr/bin"
mkdir -p "$PACKAGE_DIR/usr/share/applications"
mkdir -p "$PACKAGE_DIR/usr/share/icons/hicolor/256x256/apps"
mkdir -p "$PACKAGE_DIR/usr/share/pixmaps"
mkdir -p "$PACKAGE_DIR/usr/share/mime/packages"
mkdir -p "$PACKAGE_DIR/usr/share/doc/$APP_NAME"

# 复制可执行文件
cp "build/bin/$APP_NAME" "$PACKAGE_DIR/usr/bin/"
chmod +x "$PACKAGE_DIR/usr/bin/$APP_NAME"

# 复制桌面文件
cp "$BUILD_DIR/$APP_NAME.desktop" "$PACKAGE_DIR/usr/share/applications/"

# 复制图标文件
if [ -f "$BUILD_DIR/icon.png" ]; then
    cp "$BUILD_DIR/icon.png" "$PACKAGE_DIR/usr/share/icons/hicolor/256x256/apps/$APP_NAME.png"
    cp "$BUILD_DIR/icon.png" "$PACKAGE_DIR/usr/share/pixmaps/$APP_NAME.png"
else
    echo "Warning: icon.png not found"
fi

# 复制MIME类型定义
cp "$BUILD_DIR/$APP_NAME-mimetypes.xml" "$PACKAGE_DIR/usr/share/mime/packages/"

# 复制文档
if [ -f "LICENSE" ]; then
    cp LICENSE "$PACKAGE_DIR/usr/share/doc/$APP_NAME/copyright"
fi

if [ -f "README.md" ]; then
    cp README.md "$PACKAGE_DIR/usr/share/doc/$APP_NAME/"
fi

# 创建changelog
cat > "$PACKAGE_DIR/usr/share/doc/$APP_NAME/changelog" << EOF
$APP_NAME ($VERSION) stable; urgency=medium

  * Initial release
  * Cross-platform database management tool
  * Support for MySQL, PostgreSQL, Redis, MongoDB
  * Modern web-based user interface

 -- $MAINTAINER  $(date -R)
EOF

# 压缩changelog
gzip -9 "$PACKAGE_DIR/usr/share/doc/$APP_NAME/changelog"

# 创建DEB包
echo "Creating DEB package..."
if command -v dpkg-deb &> /dev/null; then
    DEB_DIR="$TEMP_DIR/deb"
    cp -r "$PACKAGE_DIR" "$DEB_DIR"
    
    # 创建DEBIAN控制目录
    mkdir -p "$DEB_DIR/DEBIAN"
    
    # 创建control文件
    cat > "$DEB_DIR/DEBIAN/control" << EOF
Package: $APP_NAME
Version: $VERSION
Section: utils
Priority: optional
Architecture: amd64
Depends: libc6 (>= 2.17), libgtk-3-0, libwebkit2gtk-4.0-37
Maintainer: $MAINTAINER
Description: $DESCRIPTION
 Database Manager is a cross-platform database management tool
 that supports multiple database types including MySQL, PostgreSQL,
 Redis, and MongoDB. It provides a modern web-based interface
 for database administration tasks.
EOF
    
    # 创建postinst脚本
    cat > "$DEB_DIR/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e

# 更新MIME数据库
if command -v update-mime-database &> /dev/null; then
    update-mime-database /usr/share/mime
fi

# 更新桌面数据库
if command -v update-desktop-database &> /dev/null; then
    update-desktop-database /usr/share/applications
fi

# 更新图标缓存
if command -v gtk-update-icon-cache &> /dev/null; then
    gtk-update-icon-cache -f -t /usr/share/icons/hicolor
fi

exit 0
EOF
    
    # 创建prerm脚本
    cat > "$DEB_DIR/DEBIAN/prerm" << 'EOF'
#!/bin/bash
set -e

# 在这里可以添加卸载前的清理工作

exit 0
EOF
    
    chmod +x "$DEB_DIR/DEBIAN/postinst"
    chmod +x "$DEB_DIR/DEBIAN/prerm"
    
    # 构建DEB包
    dpkg-deb --build "$DEB_DIR" "$DIST_DIR/${APP_NAME}_${VERSION}_amd64.deb"
    echo "DEB package created successfully"
else
    echo "Warning: dpkg-deb not available, skipping DEB package creation"
fi

# 创建RPM包
echo "Creating RPM package..."
if command -v rpmbuild &> /dev/null; then
    RPM_BUILD_DIR="$TEMP_DIR/rpmbuild"
    mkdir -p "$RPM_BUILD_DIR"/{BUILD,RPMS,SOURCES,SPECS,SRPMS}
    
    # 创建源码包
    tar -czf "$RPM_BUILD_DIR/SOURCES/$APP_NAME-$VERSION.tar.gz" -C "$PACKAGE_DIR" .
    
    # 创建spec文件
    cat > "$RPM_BUILD_DIR/SPECS/$APP_NAME.spec" << EOF
Name:           $APP_NAME
Version:        $VERSION
Release:        1%{?dist}
Summary:        $DESCRIPTION
License:        MIT
URL:            https://github.com/database-manager/database-manager
Source0:        %{name}-%{version}.tar.gz
BuildArch:      x86_64

Requires:       gtk3, webkit2gtk3

%description
Database Manager is a cross-platform database management tool
that supports multiple database types including MySQL, PostgreSQL,
Redis, and MongoDB. It provides a modern web-based interface
for database administration tasks.

%prep
%setup -q -c

%install
rm -rf %{buildroot}
mkdir -p %{buildroot}
cp -r * %{buildroot}/

%post
/usr/bin/update-mime-database /usr/share/mime &> /dev/null || :
/usr/bin/update-desktop-database /usr/share/applications &> /dev/null || :
/usr/bin/gtk-update-icon-cache -f -t /usr/share/icons/hicolor &> /dev/null || :

%postun
/usr/bin/update-mime-database /usr/share/mime &> /dev/null || :
/usr/bin/update-desktop-database /usr/share/applications &> /dev/null || :
/usr/bin/gtk-update-icon-cache -f -t /usr/share/icons/hicolor &> /dev/null || :

%files
/usr/bin/$APP_NAME
/usr/share/applications/$APP_NAME.desktop
/usr/share/icons/hicolor/256x256/apps/$APP_NAME.png
/usr/share/pixmaps/$APP_NAME.png
/usr/share/mime/packages/$APP_NAME-mimetypes.xml
/usr/share/doc/$APP_NAME/*

%changelog
* $(date "+%a %b %d %Y") $MAINTAINER - $VERSION-1
- Initial release
EOF
    
    # 构建RPM包
    rpmbuild --define "_topdir $RPM_BUILD_DIR" -bb "$RPM_BUILD_DIR/SPECS/$APP_NAME.spec"
    cp "$RPM_BUILD_DIR/RPMS/x86_64/$APP_NAME-$VERSION-1."*.rpm "$DIST_DIR/"
    echo "RPM package created successfully"
else
    echo "Warning: rpmbuild not available, skipping RPM package creation"
fi

# 创建AppImage
echo "Creating AppImage..."
APPIMAGE_DIR="$TEMP_DIR/AppImage"
mkdir -p "$APPIMAGE_DIR"

# 复制应用文件
cp -r "$PACKAGE_DIR/usr" "$APPIMAGE_DIR/"

# 创建AppRun脚本
cat > "$APPIMAGE_DIR/AppRun" << 'EOF'
#!/bin/bash
SELF=$(readlink -f "$0")
HERE=${SELF%/*}
export PATH="${HERE}/usr/bin/:${PATH}"
export LD_LIBRARY_PATH="${HERE}/usr/lib/:${LD_LIBRARY_PATH}"
exec "${HERE}/usr/bin/database-manager" "$@"
EOF

chmod +x "$APPIMAGE_DIR/AppRun"

# 复制桌面文件到根目录
cp "$APPIMAGE_DIR/usr/share/applications/$APP_NAME.desktop" "$APPIMAGE_DIR/"

# 复制图标到根目录
if [ -f "$APPIMAGE_DIR/usr/share/pixmaps/$APP_NAME.png" ]; then
    cp "$APPIMAGE_DIR/usr/share/pixmaps/$APP_NAME.png" "$APPIMAGE_DIR/"
fi

# 下载并使用appimagetool（如果可用）
if command -v appimagetool &> /dev/null; then
    appimagetool "$APPIMAGE_DIR" "$DIST_DIR/$APP_DISPLAY_NAME-$VERSION-x86_64.AppImage"
    echo "AppImage created successfully"
else
    echo "Warning: appimagetool not available"
    echo "Creating tar.gz archive instead..."
    cd "$APPIMAGE_DIR"
    tar -czf "$DIST_DIR/$APP_NAME-$VERSION-linux-x86_64.tar.gz" .
    cd "$PROJECT_ROOT"
fi

# 创建便携版
echo "Creating portable version..."
PORTABLE_DIR="$DIST_DIR/portable"
mkdir -p "$PORTABLE_DIR"
cp "$PACKAGE_DIR/usr/bin/$APP_NAME" "$PORTABLE_DIR/"
if [ -f "$BUILD_DIR/icon.png" ]; then
    cp "$BUILD_DIR/icon.png" "$PORTABLE_DIR/"
fi
if [ -f "LICENSE" ]; then
    cp LICENSE "$PORTABLE_DIR/"
fi
if [ -f "README.md" ]; then
    cp README.md "$PORTABLE_DIR/"
fi

# 创建便携版压缩包
cd "$DIST_DIR"
tar -czf "$APP_NAME-portable-linux-x86_64.tar.gz" portable/
cd "$PROJECT_ROOT"

# 清理临时文件
echo "Cleaning up..."
rm -rf "$TEMP_DIR"

# 显示构建结果
echo "========================================"
echo "Build completed successfully!"
echo "========================================"
echo "Build artifacts:"
ls -la "$DIST_DIR"
echo "========================================"

echo "Build script completed."
