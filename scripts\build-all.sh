#!/bin/bash

echo "Building Database Manager for all platforms..."

# 检查必要工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "Error: $1 is not installed. Please install $1 first."
        exit 1
    fi
}

echo "Checking required tools..."
check_tool "wails"
check_tool "node"
check_tool "npm"
check_tool "go"

echo "Cleaning previous builds..."
rm -rf build/bin

echo "Installing frontend dependencies..."
cd frontend
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install frontend dependencies"
    exit 1
fi

echo "Building frontend..."
npm run build
if [ $? -ne 0 ]; then
    echo "Error: Failed to build frontend"
    exit 1
fi

cd ..

echo "Building for Windows (amd64)..."
wails build -platform windows/amd64 -o database-manager-windows-amd64.exe
if [ $? -ne 0 ]; then
    echo "Error: Failed to build for Windows amd64"
    exit 1
fi

echo "Building for macOS (amd64)..."
wails build -platform darwin/amd64 -o database-manager-macos-amd64
if [ $? -ne 0 ]; then
    echo "Error: Failed to build for macOS amd64"
    exit 1
fi

echo "Building for macOS (arm64)..."
wails build -platform darwin/arm64 -o database-manager-macos-arm64
if [ $? -ne 0 ]; then
    echo "Error: Failed to build for macOS arm64"
    exit 1
fi

echo "Building for Linux (amd64)..."
wails build -platform linux/amd64 -o database-manager-linux-amd64
if [ $? -ne 0 ]; then
    echo "Error: Failed to build for Linux amd64"
    exit 1
fi

echo "All builds completed successfully!"
echo "Executables location: build/bin/"
ls -la build/bin/