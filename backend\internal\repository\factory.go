package repository

import (
	"context"
	"database-manager/backend/internal/model"
	"fmt"
	"sync"
	"time"
)

// ConnectionPool 连接池管理器
type ConnectionPool struct {
	connections map[string]DatabaseConnector
	mutex       sync.RWMutex
	maxIdle     time.Duration
	lastUsed    map[string]time.Time
}

// NewConnectionPool 创建连接池
func NewConnectionPool() *ConnectionPool {
	pool := &ConnectionPool{
		connections: make(map[string]DatabaseConnector),
		lastUsed:    make(map[string]time.Time),
		maxIdle:     30 * time.Minute, // 30分钟空闲超时
	}

	// 启动清理协程
	go pool.cleanup()

	return pool
}

// GetConnection 获取连接
func (p *ConnectionPool) GetConnection(ctx context.Context, config *model.ConnectionConfig) (DatabaseConnector, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 检查是否已有连接
	if conn, exists := p.connections[config.ID]; exists {
		if conn.IsConnected() {
			p.lastUsed[config.ID] = time.Now()
			return conn, nil
		}
		// 连接已断开，移除
		delete(p.connections, config.ID)
		delete(p.lastUsed, config.ID)
	}

	// 创建新连接
	conn, err := CreateConnector(config.Type)
	if err != nil {
		return nil, err
	}

	// 连接数据库
	if err := conn.Connect(ctx, config); err != nil {
		return nil, err
	}

	// 添加到连接池
	p.connections[config.ID] = conn
	p.lastUsed[config.ID] = time.Now()

	return conn, nil
}

// RemoveConnection 移除连接
func (p *ConnectionPool) RemoveConnection(ctx context.Context, connectionID string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if conn, exists := p.connections[connectionID]; exists {
		err := conn.Disconnect(ctx)
		delete(p.connections, connectionID)
		delete(p.lastUsed, connectionID)
		return err
	}

	return nil
}

// GetAllConnections 获取所有连接状态
func (p *ConnectionPool) GetAllConnections() map[string]*model.ConnectionStatus {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	status := make(map[string]*model.ConnectionStatus)
	for id, conn := range p.connections {
		status[id] = &model.ConnectionStatus{
			ID:        id,
			Connected: conn.IsConnected(),
			LastPing:  p.lastUsed[id],
		}
	}

	return status
}

// cleanup 清理空闲连接
func (p *ConnectionPool) cleanup() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟检查一次
	defer ticker.Stop()

	for range ticker.C {
		p.mutex.Lock()
		now := time.Now()

		for id, lastUsed := range p.lastUsed {
			if now.Sub(lastUsed) > p.maxIdle {
				if conn, exists := p.connections[id]; exists {
					conn.Disconnect(context.Background())
					delete(p.connections, id)
					delete(p.lastUsed, id)
				}
			}
		}

		p.mutex.Unlock()
	}
}

// Close 关闭连接池
func (p *ConnectionPool) Close(ctx context.Context) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	var lastErr error
	for id, conn := range p.connections {
		if err := conn.Disconnect(ctx); err != nil {
			lastErr = err
		}
		delete(p.connections, id)
		delete(p.lastUsed, id)
	}

	return lastErr
}

// CreateConnector 工厂方法：根据数据库类型创建连接器
func CreateConnector(dbType model.DatabaseType) (DatabaseConnector, error) {
	switch dbType {
	case model.DatabaseTypeRedis:
		return NewRedisRepository(), nil
	case model.DatabaseTypeMySQL:
		return NewMySQLRepository(), nil
	case model.DatabaseTypePostgreSQL:
		return NewPostgreSQLRepository(), nil
	case model.DatabaseTypeMongoDB:
		return NewMongoDBRepository(), nil
	default:
		return nil, fmt.Errorf("unsupported database type: %s", dbType)
	}
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	pool    *ConnectionPool
	configs map[string]*model.ConnectionConfig
	mutex   sync.RWMutex
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		pool:    NewConnectionPool(),
		configs: make(map[string]*model.ConnectionConfig),
	}
}

// AddConnection 添加连接配置
func (cm *ConnectionManager) AddConnection(config *model.ConnectionConfig) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	config.CreatedAt = time.Now()
	config.UpdatedAt = time.Now()
	cm.configs[config.ID] = config
}

// UpdateConnection 更新连接配置
func (cm *ConnectionManager) UpdateConnection(config *model.ConnectionConfig) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if _, exists := cm.configs[config.ID]; !exists {
		return fmt.Errorf("connection not found: %s", config.ID)
	}

	config.UpdatedAt = time.Now()
	cm.configs[config.ID] = config

	// 如果连接已存在，需要重新连接
	cm.pool.RemoveConnection(context.Background(), config.ID)

	return nil
}

// RemoveConnection 移除连接配置
func (cm *ConnectionManager) RemoveConnection(ctx context.Context, connectionID string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 从连接池中移除
	if err := cm.pool.RemoveConnection(ctx, connectionID); err != nil {
		return err
	}

	// 从配置中移除
	delete(cm.configs, connectionID)

	return nil
}

// GetConnection 获取连接
func (cm *ConnectionManager) GetConnection(ctx context.Context, connectionID string) (DatabaseConnector, error) {
	cm.mutex.RLock()
	config, exists := cm.configs[connectionID]
	cm.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("connection not found: %s", connectionID)
	}

	return cm.pool.GetConnection(ctx, config)
}

// GetAllConfigs 获取所有连接配置
func (cm *ConnectionManager) GetAllConfigs() []*model.ConnectionConfig {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	configs := make([]*model.ConnectionConfig, 0, len(cm.configs))
	for _, config := range cm.configs {
		configs = append(configs, config)
	}

	return configs
}

// GetConnectionStatus 获取连接状态
func (cm *ConnectionManager) GetConnectionStatus() map[string]*model.ConnectionStatus {
	return cm.pool.GetAllConnections()
}

// TestConnection 测试连接
func (cm *ConnectionManager) TestConnection(ctx context.Context, config *model.ConnectionConfig) error {
	connector, err := CreateConnector(config.Type)
	if err != nil {
		return err
	}

	if err := connector.Connect(ctx, config); err != nil {
		return err
	}

	defer connector.Disconnect(ctx)

	return connector.Ping(ctx)
}

// Close 关闭连接管理器
func (cm *ConnectionManager) Close(ctx context.Context) error {
	return cm.pool.Close(ctx)
}
