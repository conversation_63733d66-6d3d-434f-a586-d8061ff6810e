name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每天UTC时间2点运行测试
    - cron: '0 2 * * *'

env:
  GO_VERSION: '1.21'
  NODE_VERSION: '18'

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Install Go dependencies
      run: go mod download
    
    - name: Run Go unit tests
      run: |
        go test -v -race -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html
    
    - name: Run frontend unit tests
      run: |
        cd frontend
        npm test -- --coverage --watchAll=false
    
    - name: Upload Go coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: backend
        name: backend-coverage
    
    - name: Upload frontend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: testdb
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      mongodb:
        image: mongo:7
        ports:
          - 27017:27017
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Install Go dependencies
      run: go mod download
    
    - name: Wait for services
      run: |
        # 等待服务启动
        sleep 30
    
    - name: Run integration tests
      run: |
        go test -v -tags=integration ./tests/...
      env:
        REDIS_URL: redis://localhost:6379
        MYSQL_URL: mysql://root:testpassword@localhost:3306/testdb
        POSTGRES_URL: postgres://postgres:testpassword@localhost:5432/testdb
        MONGODB_URL: mongodb://localhost:27017

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Install Go dependencies
      run: go mod download
    
    - name: Run performance tests
      run: |
        go test -v -bench=. -benchmem ./tests/...
    
    - name: Run memory tests
      run: |
        go test -v -run TestMemory ./tests/...

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: './...'
    
    - name: Run Nancy vulnerability scanner
      run: |
        go install github.com/sonatypecommunity/nancy@latest
        go list -json -deps ./... | nancy sleuth

  lint:
    name: Lint
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        args: --timeout=5m
    
    - name: Run frontend lint
      run: |
        cd frontend
        npm run lint
    
    - name: Check Go formatting
      run: |
        if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
          echo "Go code is not formatted:"
          gofmt -s -l .
          exit 1
        fi
    
    - name: Check Go modules
      run: |
        go mod tidy
        if ! git diff --quiet go.mod go.sum; then
          echo "Go modules are not tidy"
          exit 1
        fi

  build-test:
    name: Build Test
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install Wails
      run: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Install Go dependencies
      run: go mod download
    
    - name: Test build
      run: wails build -clean
