@echo off
echo Building Database Manager for Windows...

REM 检查Wails是否安装
wails version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Wails is not installed. Please install Wails first.
    echo Run: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed. Please install Node.js first.
    exit /b 1
)

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed. Please install Go first.
    exit /b 1
)

echo Cleaning previous builds...
if exist "build\bin" rmdir /s /q "build\bin"

echo Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies
    exit /b 1
)

echo Building frontend...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build frontend
    exit /b 1
)

cd ..

echo Setting up version information...
call "%~dp0build-version.bat"

echo Building Wails application...
wails build -platform windows/amd64 -clean -ldflags "%BUILD_LDFLAGS%"
if %errorlevel% neq 0 (
    echo Error: Failed to build Wails application
    exit /b 1
)

echo Checking build result...
if exist "build\bin\database-manager.exe" (
    echo Build completed successfully!
    echo Executable location: build\bin\database-manager.exe

    REM 获取文件大小
    for %%I in ("build\bin\database-manager.exe") do set size=%%~zI
    echo File size: %size% bytes

    REM 询问是否运行应用程序
    set /p run_app="Do you want to run the application? (y/n): "
    if /i "%run_app%"=="y" (
        echo Starting Database Manager...
        start "" "build\bin\database-manager.exe"
    )
) else (
    echo Error: Build failed - executable not found
    exit /b 1
)

pause