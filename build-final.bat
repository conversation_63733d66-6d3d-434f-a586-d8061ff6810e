@echo off
REM Database Manager Final Build Script
REM 最终构建脚本 - 生成可分发的应用程序

echo ========================================
echo Database Manager Final Build Script
echo ========================================

set APP_NAME=database-manager
set VERSION=1.0.0
set BUILD_DIR=%~dp0
set DIST_DIR=%BUILD_DIR%dist

echo Building Database Manager v%VERSION%...

REM 清理之前的构建
echo Cleaning previous builds...
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
mkdir "%DIST_DIR%"

REM 安装前端依赖
echo Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies
    exit /b 1
)

REM 构建前端
echo Building frontend...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build frontend
    exit /b 1
)

REM 回到根目录
cd ..

REM 安装Go依赖
echo Installing Go dependencies...
go mod tidy
if %errorlevel% neq 0 (
    echo Error: Failed to install Go dependencies
    exit /b 1
)

REM 构建Wails应用
echo Building Wails application...
wails build -platform windows/amd64 -clean -nopackage
if %errorlevel% neq 0 (
    echo Error: Failed to build Wails application
    exit /b 1
)

REM 复制构建产物到分发目录
echo Copying build artifacts...
copy "build\bin\%APP_NAME%.exe" "%DIST_DIR%\"

REM 复制许可证和说明文件
if exist "LICENSE" copy "LICENSE" "%DIST_DIR%\"
if exist "README.md" copy "README.md" "%DIST_DIR%\"

REM 创建版本信息文件
echo Creating version info...
echo Database Manager v%VERSION% > "%DIST_DIR%\VERSION.txt"
echo Build Date: %DATE% %TIME% >> "%DIST_DIR%\VERSION.txt"
echo Platform: Windows x64 >> "%DIST_DIR%\VERSION.txt"

REM 获取文件大小
for %%I in ("%DIST_DIR%\%APP_NAME%.exe") do set size=%%~zI

echo ========================================
echo Build completed successfully!
echo ========================================
echo Application: %DIST_DIR%\%APP_NAME%.exe
echo File size: %size% bytes
echo Version: %VERSION%
echo ========================================

REM 询问是否运行应用程序
set /p run_app="Do you want to run the application? (y/n): "
if /i "%run_app%"=="y" (
    echo Starting Database Manager...
    start "" "%DIST_DIR%\%APP_NAME%.exe"
)

echo Build completed. Files are in: %DIST_DIR%
pause
