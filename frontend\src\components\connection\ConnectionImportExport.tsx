import React, { useState } from 'react';
import { Button, Input } from '../common';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
  connected: boolean;
  lastConnected?: string;
  connectionCount?: number;
  tags?: string[];
  group?: string;
  favorite?: boolean;
  created_at: string;
  updated_at: string;
}

interface ConnectionImportExportProps {
  connections: Connection[];
  onImport: (connections: Connection[]) => void;
  onClose: () => void;
}

const ConnectionImportExport: React.FC<ConnectionImportExportProps> = ({
  connections,
  onImport,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<'export' | 'import'>('export');
  const [importData, setImportData] = useState('');
  const [selectedConnections, setSelectedConnections] = useState<Set<string>>(new Set());
  const [exportFormat, setExportFormat] = useState<'json' | 'csv'>('json');
  const [includePasswords, setIncludePasswords] = useState(false);
  const [importError, setImportError] = useState('');

  // 导出连接
  const exportConnections = () => {
    const connectionsToExport = connections.filter(conn => 
      selectedConnections.size === 0 || selectedConnections.has(conn.id)
    );

    const exportData = connectionsToExport.map(conn => ({
      name: conn.name,
      type: conn.type,
      host: conn.host,
      port: conn.port,
      username: conn.username,
      password: includePasswords ? conn.password : '',
      database: conn.database,
      ssl: conn.ssl,
      tags: conn.tags,
      group: conn.group,
      favorite: conn.favorite,
    }));

    if (exportFormat === 'json') {
      const jsonData = JSON.stringify(exportData, null, 2);
      downloadFile(jsonData, 'connections.json', 'application/json');
    } else {
      const csvData = convertToCSV(exportData);
      downloadFile(csvData, 'connections.csv', 'text/csv');
    }
  };

  // 转换为CSV格式
  const convertToCSV = (data: any[]) => {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => 
      headers.map(header => {
        const value = row[header];
        if (Array.isArray(value)) {
          return `"${value.join(';')}"`;
        }
        return `"${String(value).replace(/"/g, '""')}"`;
      }).join(',')
    );

    return [csvHeaders, ...csvRows].join('\n');
  };

  // 下载文件
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 导入连接
  const importConnections = () => {
    try {
      setImportError('');
      
      if (!importData.trim()) {
        setImportError('Please paste connection data');
        return;
      }

      let parsedData;
      
      // 尝试解析JSON
      try {
        parsedData = JSON.parse(importData);
      } catch {
        // 尝试解析CSV
        parsedData = parseCSV(importData);
      }

      if (!Array.isArray(parsedData)) {
        setImportError('Invalid data format. Expected an array of connections.');
        return;
      }

      // 验证和转换数据
      const validConnections = parsedData.map((conn: any, index: number) => {
        if (!conn.name || !conn.type || !conn.host) {
          throw new Error(`Invalid connection at row ${index + 1}: missing required fields`);
        }

        return {
          id: `imported-${Date.now()}-${index}`,
          name: conn.name,
          type: conn.type.toLowerCase(),
          host: conn.host,
          port: parseInt(conn.port) || getDefaultPort(conn.type),
          username: conn.username || '',
          password: conn.password || '',
          database: conn.database || '',
          ssl: Boolean(conn.ssl),
          connected: false,
          tags: Array.isArray(conn.tags) ? conn.tags : (conn.tags ? conn.tags.split(';') : []),
          group: conn.group || 'Imported',
          favorite: Boolean(conn.favorite),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      });

      onImport(validConnections);
      setImportData('');
    } catch (error) {
      setImportError(error instanceof Error ? error.message : 'Failed to import connections');
    }
  };

  // 解析CSV
  const parseCSV = (csvData: string) => {
    const lines = csvData.trim().split('\n');
    if (lines.length < 2) {
      throw new Error('CSV must have at least a header and one data row');
    }

    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
      const row: any = {};
      
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      
      data.push(row);
    }

    return data;
  };

  // 获取默认端口
  const getDefaultPort = (type: string) => {
    switch (type.toLowerCase()) {
      case 'mysql': return 3306;
      case 'postgresql': return 5432;
      case 'redis': return 6379;
      case 'mongodb': return 27017;
      default: return 3306;
    }
  };

  // 切换连接选择
  const toggleConnectionSelection = (connectionId: string) => {
    const newSelected = new Set(selectedConnections);
    if (newSelected.has(connectionId)) {
      newSelected.delete(connectionId);
    } else {
      newSelected.add(connectionId);
    }
    setSelectedConnections(newSelected);
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedConnections.size === connections.length) {
      setSelectedConnections(new Set());
    } else {
      setSelectedConnections(new Set(connections.map(c => c.id)));
    }
  };

  return (
    <div className="space-y-6">
      {/* 标签页 */}
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setActiveTab('export')}
          className={`px-4 py-2 font-medium text-sm border-b-2 ${
            activeTab === 'export'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
        >
          Export Connections
        </button>
        <button
          onClick={() => setActiveTab('import')}
          className={`px-4 py-2 font-medium text-sm border-b-2 ${
            activeTab === 'import'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
        >
          Import Connections
        </button>
      </div>

      {activeTab === 'export' ? (
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Export Connections
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Select connections to export and choose your preferred format.
            </p>
          </div>

          {/* 连接选择 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Select Connections
              </label>
              <button
                onClick={toggleSelectAll}
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                {selectedConnections.size === connections.length ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            
            <div className="max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-2">
              {connections.map(connection => (
                <label
                  key={connection.id}
                  className="flex items-center space-x-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedConnections.has(connection.id)}
                    onChange={() => toggleConnectionSelection(connection.id)}
                    className="rounded"
                  />
                  <span className="text-sm text-gray-900 dark:text-white">
                    {connection.name} ({connection.type})
                  </span>
                </label>
              ))}
            </div>
            
            {selectedConnections.size === 0 && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No connections selected. All connections will be exported.
              </p>
            )}
          </div>

          {/* 导出选项 */}
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Export Format
              </label>
              <div className="mt-1 flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="json"
                    checked={exportFormat === 'json'}
                    onChange={(e) => setExportFormat(e.target.value as 'json')}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-900 dark:text-white">JSON</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="csv"
                    checked={exportFormat === 'csv'}
                    onChange={(e) => setExportFormat(e.target.value as 'csv')}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-900 dark:text-white">CSV</span>
                </label>
              </div>
            </div>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={includePasswords}
                onChange={(e) => setIncludePasswords(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-900 dark:text-white">
                Include passwords (not recommended for sharing)
              </span>
            </label>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={exportConnections}>
              Export {selectedConnections.size || connections.length} Connections
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Import Connections
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Paste JSON or CSV data to import connections. Supported formats:
            </p>
            <ul className="text-sm text-gray-600 dark:text-gray-400 list-disc list-inside space-y-1">
              <li>JSON array of connection objects</li>
              <li>CSV with headers: name, type, host, port, username, password, database, ssl</li>
            </ul>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Connection Data
            </label>
            <textarea
              value={importData}
              onChange={(e) => setImportData(e.target.value)}
              placeholder="Paste your connection data here..."
              className="w-full h-64 p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm"
            />
          </div>

          {importError && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <p className="text-sm text-red-600 dark:text-red-400">{importError}</p>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={importConnections} disabled={!importData.trim()}>
              Import Connections
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConnectionImportExport;
