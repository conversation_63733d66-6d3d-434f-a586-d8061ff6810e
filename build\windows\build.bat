@echo off
REM Database Manager Windows Build Script
REM 构建Windows平台的可执行文件和安装程序

echo ========================================
echo Database Manager Windows Build Script
echo ========================================

REM 设置变量
set APP_NAME=database-manager
set VERSION=1.0.0
set BUILD_DIR=%~dp0
set PROJECT_ROOT=%BUILD_DIR%\..\..
set DIST_DIR=%BUILD_DIR%\dist
set TEMP_DIR=%BUILD_DIR%\temp

REM 检查必要工具
echo Checking required tools...

REM 检查Go
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed or not in PATH
    exit /b 1
)

REM 检查Wails
wails version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Wails is not installed or not in PATH
    echo Please install Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    exit /b 1
)

REM 检查Node.js (用于前端构建)
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    exit /b 1
)

REM 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not installed or not in PATH
    exit /b 1
)

echo All required tools are available.

REM 创建构建目录
echo Creating build directories...
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%DIST_DIR%"
mkdir "%TEMP_DIR%"

REM 切换到项目根目录
cd /d "%PROJECT_ROOT%"

REM 安装前端依赖
echo Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies
    exit /b 1
)

REM 构建前端
echo Building frontend...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build frontend
    exit /b 1
)

REM 回到项目根目录
cd /d "%PROJECT_ROOT%"

REM 安装Go依赖
echo Installing Go dependencies...
go mod tidy
if %errorlevel% neq 0 (
    echo Error: Failed to install Go dependencies
    exit /b 1
)

REM 构建应用程序
echo Building application...
wails build -platform windows/amd64 -clean
if %errorlevel% neq 0 (
    echo Error: Failed to build application
    exit /b 1
)

REM 复制构建产物到临时目录
echo Copying build artifacts...
copy "build\bin\%APP_NAME%.exe" "%TEMP_DIR%\"
copy "%BUILD_DIR%\icon.ico" "%TEMP_DIR%\"
copy "LICENSE" "%TEMP_DIR%\license.txt"
copy "README.md" "%TEMP_DIR%\"

REM 复制资源文件
if exist "resources" (
    xcopy "resources" "%TEMP_DIR%\resources\" /e /i /h
)

REM 检查NSIS是否可用
echo Checking for NSIS...
makensis /VERSION >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: NSIS is not installed or not in PATH
    echo Skipping installer creation
    echo You can manually create installer using NSIS with installer.nsi
    goto :skip_installer
)

REM 创建安装程序
echo Creating installer...
cd /d "%BUILD_DIR%"
makensis installer.nsi
if %errorlevel% neq 0 (
    echo Error: Failed to create installer
    exit /b 1
)

REM 移动安装程序到分发目录
move "%APP_NAME%-setup.exe" "%DIST_DIR%\"

:skip_installer

REM 创建便携版
echo Creating portable version...
mkdir "%DIST_DIR%\portable"
copy "%TEMP_DIR%\%APP_NAME%.exe" "%DIST_DIR%\portable\"
copy "%TEMP_DIR%\icon.ico" "%DIST_DIR%\portable\"
copy "%TEMP_DIR%\license.txt" "%DIST_DIR%\portable\"
copy "%TEMP_DIR%\README.md" "%DIST_DIR%\portable\"
if exist "%TEMP_DIR%\resources" (
    xcopy "%TEMP_DIR%\resources" "%DIST_DIR%\portable\resources\" /e /i /h
)

REM 创建便携版压缩包
echo Creating portable archive...
cd /d "%DIST_DIR%"
powershell -command "Compress-Archive -Path 'portable\*' -DestinationPath '%APP_NAME%-portable-windows-amd64.zip' -Force"

REM 清理临时文件
echo Cleaning up...
rmdir /s /q "%TEMP_DIR%"

REM 显示构建结果
echo ========================================
echo Build completed successfully!
echo ========================================
echo Build artifacts:
if exist "%DIST_DIR%\%APP_NAME%-setup.exe" (
    echo - Installer: %DIST_DIR%\%APP_NAME%-setup.exe
)
echo - Portable: %DIST_DIR%\%APP_NAME%-portable-windows-amd64.zip
echo - Executable: %DIST_DIR%\portable\%APP_NAME%.exe
echo ========================================

REM 询问是否运行应用程序
set /p run_app="Do you want to run the application? (y/n): "
if /i "%run_app%"=="y" (
    start "" "%DIST_DIR%\portable\%APP_NAME%.exe"
)

echo Build script completed.
pause
