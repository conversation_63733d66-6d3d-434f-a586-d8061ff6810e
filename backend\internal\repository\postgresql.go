package repository

import (
	"context"
	"database-manager/backend/internal/model"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/lib/pq"
)

// PostgreSQLRepository PostgreSQL连接器实现
type PostgreSQLRepository struct {
	db        *sql.DB
	config    *model.ConnectionConfig
	connected bool
	tx        *sql.Tx // 事务对象
}

// NewPostgreSQLRepository 创建PostgreSQL连接器
func NewPostgreSQLRepository() *PostgreSQLRepository {
	return &PostgreSQLRepository{
		connected: false,
	}
}

// Connect 连接PostgreSQL数据库
func (p *PostgreSQLRepository) Connect(ctx context.Context, config *model.ConnectionConfig) error {
	p.config = config

	// 构建连接字符串
	connStr := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s",
		config.Host,
		config.Port,
		config.Username,
		config.Password,
		config.Database,
	)

	// 添加SSL配置
	if config.SSL {
		connStr += " sslmode=require"
	} else {
		connStr += " sslmode=disable"
	}

	// 添加其他选项
	if config.Options != nil {
		for key, value := range config.Options {
			connStr += fmt.Sprintf(" %s=%s", key, value)
		}
	}

	// 打开数据库连接
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return fmt.Errorf("failed to open PostgreSQL connection: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	p.db = db

	// 测试连接
	if err := p.Ping(ctx); err != nil {
		p.db.Close()
		return fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	p.connected = true
	return nil
}

// Disconnect 断开连接
func (p *PostgreSQLRepository) Disconnect(ctx context.Context) error {
	if p.tx != nil {
		p.tx.Rollback()
		p.tx = nil
	}

	if p.db != nil {
		err := p.db.Close()
		p.db = nil
		p.connected = false
		return err
	}
	return nil
}

// Ping 测试连接
func (p *PostgreSQLRepository) Ping(ctx context.Context) error {
	if p.db == nil {
		return fmt.Errorf("PostgreSQL connection not initialized")
	}

	return p.db.PingContext(ctx)
}

// IsConnected 检查连接状态
func (p *PostgreSQLRepository) IsConnected() bool {
	return p.connected && p.db != nil
}

// GetDatabaseInfo 获取数据库信息
func (p *PostgreSQLRepository) GetDatabaseInfo(ctx context.Context) (*model.DatabaseInfo, error) {
	if !p.IsConnected() {
		return nil, fmt.Errorf("not connected to PostgreSQL")
	}

	// 获取PostgreSQL版本
	var version string
	err := p.db.QueryRowContext(ctx, "SELECT version()").Scan(&version)
	if err != nil {
		version = "unknown"
	}

	// 获取数据库大小
	var size int64
	sizeQuery := `
		SELECT pg_database_size($1)
	`
	err = p.db.QueryRowContext(ctx, sizeQuery, p.config.Database).Scan(&size)
	if err != nil {
		size = 0
	}

	// 获取表数量
	var tableCount int
	tableQuery := `
		SELECT COUNT(*)
		FROM information_schema.tables
		WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
	`
	err = p.db.QueryRowContext(ctx, tableQuery).Scan(&tableCount)
	if err != nil {
		tableCount = 0
	}

	return &model.DatabaseInfo{
		Name:    p.config.Database,
		Size:    size,
		Tables:  tableCount,
		Version: version,
	}, nil
}

// GetTables 获取表列表
func (p *PostgreSQLRepository) GetTables(ctx context.Context, database string) ([]*model.TableInfo, error) {
	if !p.IsConnected() {
		return nil, fmt.Errorf("not connected to PostgreSQL")
	}

	query := `
		SELECT
			t.table_name,
			t.table_type,
			COALESCE(s.n_tup_ins + s.n_tup_upd + s.n_tup_del, 0) as estimated_rows,
			COALESCE(pg_total_relation_size(c.oid), 0) as table_size,
			COALESCE(obj_description(c.oid), '') as table_comment
		FROM information_schema.tables t
		LEFT JOIN pg_class c ON c.relname = t.table_name
		LEFT JOIN pg_stat_user_tables s ON s.relname = t.table_name
		WHERE t.table_schema = 'public' AND t.table_type = 'BASE TABLE'
		ORDER BY t.table_name
	`

	rows, err := p.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query tables: %w", err)
	}
	defer rows.Close()

	var tables []*model.TableInfo
	for rows.Next() {
		var table model.TableInfo
		err := rows.Scan(
			&table.Name,
			&table.Type,
			&table.Rows,
			&table.Size,
			&table.Comment,
		)
		if err != nil {
			continue
		}
		tables = append(tables, &table)
	}

	return tables, nil
}

// GetColumns 获取表列信息
func (p *PostgreSQLRepository) GetColumns(ctx context.Context, database, table string) ([]*model.ColumnInfo, error) {
	if !p.IsConnected() {
		return nil, fmt.Errorf("not connected to PostgreSQL")
	}

	query := `
		SELECT
			column_name,
			data_type,
			is_nullable,
			COALESCE(column_default, '') as column_default,
			COALESCE(col_description(pgc.oid, ordinal_position), '') as column_comment,
			CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
			CASE WHEN column_default LIKE 'nextval%' THEN true ELSE false END as is_auto_increment
		FROM information_schema.columns c
		LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
		LEFT JOIN (
			SELECT ku.column_name
			FROM information_schema.table_constraints tc
			JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
			WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = $2
		) pk ON pk.column_name = c.column_name
		WHERE c.table_schema = 'public' AND c.table_name = $2
		ORDER BY c.ordinal_position
	`

	rows, err := p.db.QueryContext(ctx, query, database, table)
	if err != nil {
		return nil, fmt.Errorf("failed to query columns: %w", err)
	}
	defer rows.Close()

	var columns []*model.ColumnInfo
	for rows.Next() {
		var column model.ColumnInfo
		var nullable string

		err := rows.Scan(
			&column.Name,
			&column.Type,
			&nullable,
			&column.DefaultValue,
			&column.Comment,
			&column.IsPrimaryKey,
			&column.IsAutoIncrement,
		)
		if err != nil {
			continue
		}

		column.Nullable = nullable == "YES"
		columns = append(columns, &column)
	}

	return columns, nil
}

// ExecuteQuery 执行查询
func (p *PostgreSQLRepository) ExecuteQuery(ctx context.Context, query string, params ...any) (*model.QueryResult, error) {
	if !p.IsConnected() {
		return nil, fmt.Errorf("not connected to PostgreSQL")
	}

	start := time.Now()

	// 执行查询
	rows, err := p.db.QueryContext(ctx, query, params...)
	if err != nil {
		return &model.QueryResult{
			Columns: []string{"Error"},
			Rows:    [][]any{},
			Total:   0,
			Elapsed: time.Since(start),
			Error:   err.Error(),
		}, nil
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	// 读取数据
	var resultRows [][]any
	for rows.Next() {
		// 创建扫描目标
		values := make([]any, len(columns))
		valuePtrs := make([]any, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描行数据
		if err := rows.Scan(valuePtrs...); err != nil {
			continue
		}

		// 处理NULL值和特殊类型
		row := make([]any, len(columns))
		for i, val := range values {
			if val == nil {
				row[i] = nil
			} else {
				switch v := val.(type) {
				case []byte:
					row[i] = string(v)
				default:
					row[i] = v
				}
			}
		}

		resultRows = append(resultRows, row)
	}

	return &model.QueryResult{
		Columns: columns,
		Rows:    resultRows,
		Total:   int64(len(resultRows)),
		Elapsed: time.Since(start),
	}, nil
}

// ExecuteCommand 执行命令
func (p *PostgreSQLRepository) ExecuteCommand(ctx context.Context, command string, params ...any) error {
	if !p.IsConnected() {
		return fmt.Errorf("not connected to PostgreSQL")
	}

	var err error
	if p.tx != nil {
		// 在事务中执行
		_, err = p.tx.ExecContext(ctx, command, params...)
	} else {
		// 直接执行
		_, err = p.db.ExecContext(ctx, command, params...)
	}

	return err
}

// BeginTransaction 开始事务
func (p *PostgreSQLRepository) BeginTransaction(ctx context.Context) error {
	if !p.IsConnected() {
		return fmt.Errorf("not connected to PostgreSQL")
	}

	if p.tx != nil {
		return fmt.Errorf("transaction already started")
	}

	tx, err := p.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	p.tx = tx
	return nil
}

// CommitTransaction 提交事务
func (p *PostgreSQLRepository) CommitTransaction(ctx context.Context) error {
	if p.tx == nil {
		return fmt.Errorf("no active transaction")
	}

	err := p.tx.Commit()
	p.tx = nil
	return err
}

// RollbackTransaction 回滚事务
func (p *PostgreSQLRepository) RollbackTransaction(ctx context.Context) error {
	if p.tx == nil {
		return fmt.Errorf("no active transaction")
	}

	err := p.tx.Rollback()
	p.tx = nil
	return err
}

// GetSchemas 获取模式列表
func (p *PostgreSQLRepository) GetSchemas(ctx context.Context) ([]string, error) {
	if !p.IsConnected() {
		return nil, fmt.Errorf("not connected to PostgreSQL")
	}

	query := `
		SELECT schema_name
		FROM information_schema.schemata
		WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
		AND schema_name NOT LIKE 'pg_temp_%'
		AND schema_name NOT LIKE 'pg_toast_temp_%'
		ORDER BY schema_name
	`

	rows, err := p.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query schemas: %w", err)
	}
	defer rows.Close()

	var schemas []string
	for rows.Next() {
		var schema string
		if err := rows.Scan(&schema); err != nil {
			continue
		}
		schemas = append(schemas, schema)
	}

	return schemas, nil
}
