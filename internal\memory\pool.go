package memory

import (
	"fmt"
	"sync"
	"time"
)

// ObjectPool 对象池接口
type ObjectPool interface {
	Get() interface{}
	Put(interface{})
	Clear()
	Size() int
}

// Pool 通用对象池
type Pool struct {
	pool    sync.Pool
	maxSize int
	current int
	mutex   sync.RWMutex
	factory func() interface{}
	reset   func(interface{})
}

// NewPool 创建新的对象池
func NewPool(maxSize int, factory func() interface{}, reset func(interface{})) *Pool {
	return &Pool{
		maxSize: maxSize,
		factory: factory,
		reset:   reset,
		pool: sync.Pool{
			New: factory,
		},
	}
}

// Get 从池中获取对象
func (p *Pool) Get() interface{} {
	return p.pool.Get()
}

// Put 将对象放回池中
func (p *Pool) Put(obj interface{}) {
	if obj == nil {
		return
	}

	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.current >= p.maxSize {
		// 池已满，丢弃对象
		return
	}

	// 重置对象状态
	if p.reset != nil {
		p.reset(obj)
	}

	p.pool.Put(obj)
	p.current++
}

// Clear 清空池
func (p *Pool) Clear() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 创建新的sync.Pool
	p.pool = sync.Pool{
		New: p.factory,
	}
	p.current = 0
}

// Size 获取池中对象数量
func (p *Pool) Size() int {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.current
}

// BufferPool 字节缓冲池
type BufferPool struct {
	pool *Pool
}

// NewBufferPool 创建字节缓冲池
func NewBufferPool(maxSize int, bufferSize int) *BufferPool {
	return &BufferPool{
		pool: NewPool(
			maxSize,
			func() interface{} {
				return make([]byte, 0, bufferSize)
			},
			func(obj interface{}) {
				if buf, ok := obj.([]byte); ok {
					// 重置缓冲区长度为0，但保留容量
					buf = buf[:0]
				}
			},
		),
	}
}

// Get 获取缓冲区
func (bp *BufferPool) Get() []byte {
	return bp.pool.Get().([]byte)
}

// Put 归还缓冲区
func (bp *BufferPool) Put(buf []byte) {
	bp.pool.Put(buf)
}

// StringPool 字符串池
type StringPool struct {
	pool *Pool
}

// NewStringPool 创建字符串池
func NewStringPool(maxSize int) *StringPool {
	return &StringPool{
		pool: NewPool(
			maxSize,
			func() interface{} {
				return ""
			},
			func(obj interface{}) {
				// 字符串是不可变的，无需重置
			},
		),
	}
}

// Get 获取字符串
func (sp *StringPool) Get() string {
	return sp.pool.Get().(string)
}

// Put 归还字符串
func (sp *StringPool) Put(s string) {
	sp.pool.Put(s)
}

// ConnectionPool 连接池
type ConnectionPool struct {
	connections chan interface{}
	factory     func() (interface{}, error)
	close       func(interface{}) error
	ping        func(interface{}) error
	maxIdle     int
	maxOpen     int
	maxLifetime time.Duration
	mutex       sync.RWMutex
	closed      bool
}

// NewConnectionPool 创建连接池
func NewConnectionPool(maxIdle, maxOpen int, maxLifetime time.Duration,
	factory func() (interface{}, error),
	close func(interface{}) error,
	ping func(interface{}) error) *ConnectionPool {

	return &ConnectionPool{
		connections: make(chan interface{}, maxIdle),
		factory:     factory,
		close:       close,
		ping:        ping,
		maxIdle:     maxIdle,
		maxOpen:     maxOpen,
		maxLifetime: maxLifetime,
	}
}

// Get 获取连接
func (cp *ConnectionPool) Get() (interface{}, error) {
	cp.mutex.RLock()
	if cp.closed {
		cp.mutex.RUnlock()
		return nil, ErrPoolClosed
	}
	cp.mutex.RUnlock()

	select {
	case conn := <-cp.connections:
		// 检查连接是否有效
		if cp.ping != nil && cp.ping(conn) != nil {
			// 连接无效，创建新连接
			if cp.close != nil {
				cp.close(conn)
			}
			return cp.factory()
		}
		return conn, nil
	default:
		// 池中没有可用连接，创建新连接
		return cp.factory()
	}
}

// Put 归还连接
func (cp *ConnectionPool) Put(conn interface{}) error {
	if conn == nil {
		return nil
	}

	cp.mutex.RLock()
	if cp.closed {
		cp.mutex.RUnlock()
		if cp.close != nil {
			return cp.close(conn)
		}
		return nil
	}
	cp.mutex.RUnlock()

	select {
	case cp.connections <- conn:
		return nil
	default:
		// 池已满，关闭连接
		if cp.close != nil {
			return cp.close(conn)
		}
		return nil
	}
}

// Close 关闭连接池
func (cp *ConnectionPool) Close() error {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	if cp.closed {
		return nil
	}

	cp.closed = true
	close(cp.connections)

	// 关闭所有连接
	for conn := range cp.connections {
		if cp.close != nil {
			cp.close(conn)
		}
	}

	return nil
}

// Stats 获取连接池统计信息
func (cp *ConnectionPool) Stats() map[string]int {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	return map[string]int{
		"idle":    len(cp.connections),
		"maxIdle": cp.maxIdle,
		"maxOpen": cp.maxOpen,
	}
}

// 错误定义
var (
	ErrPoolClosed = fmt.Errorf("pool is closed")
)
