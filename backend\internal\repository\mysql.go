package repository

import (
	"context"
	"database-manager/backend/internal/model"
	"database/sql"
	"fmt"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLRepository MySQL连接器实现
type MySQLRepository struct {
	db        *sql.DB
	config    *model.ConnectionConfig
	connected bool
	tx        *sql.Tx // 事务对象
}

// NewMySQLRepository 创建MySQL连接器
func NewMySQLRepository() *MySQLRepository {
	return &MySQLRepository{
		connected: false,
	}
}

// Connect 连接MySQL数据库
func (m *MySQLRepository) Connect(ctx context.Context, config *model.ConnectionConfig) error {
	m.config = config

	// 构建DSN (Data Source Name)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
	)

	// 添加SSL配置
	if config.SSL {
		dsn += "?tls=true"
	} else {
		dsn += "?tls=false"
	}

	// 添加其他选项
	if config.Options != nil {
		params := []string{}
		for key, value := range config.Options {
			params = append(params, fmt.Sprintf("%s=%s", key, value))
		}
		if len(params) > 0 {
			if strings.Contains(dsn, "?") {
				dsn += "&" + strings.Join(params, "&")
			} else {
				dsn += "?" + strings.Join(params, "&")
			}
		}
	}

	// 打开数据库连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	m.db = db

	// 测试连接
	if err := m.Ping(ctx); err != nil {
		m.db.Close()
		return fmt.Errorf("failed to ping MySQL: %w", err)
	}

	m.connected = true
	return nil
}

// Disconnect 断开连接
func (m *MySQLRepository) Disconnect(ctx context.Context) error {
	if m.tx != nil {
		m.tx.Rollback()
		m.tx = nil
	}

	if m.db != nil {
		err := m.db.Close()
		m.db = nil
		m.connected = false
		return err
	}
	return nil
}

// Ping 测试连接
func (m *MySQLRepository) Ping(ctx context.Context) error {
	if m.db == nil {
		return fmt.Errorf("MySQL connection not initialized")
	}

	return m.db.PingContext(ctx)
}

// IsConnected 检查连接状态
func (m *MySQLRepository) IsConnected() bool {
	return m.connected && m.db != nil
}

// GetDatabaseInfo 获取数据库信息
func (m *MySQLRepository) GetDatabaseInfo(ctx context.Context) (*model.DatabaseInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MySQL")
	}

	// 获取MySQL版本
	var version string
	err := m.db.QueryRowContext(ctx, "SELECT VERSION()").Scan(&version)
	if err != nil {
		version = "unknown"
	}

	// 获取数据库大小
	var size sql.NullInt64
	sizeQuery := `
		SELECT ROUND(SUM(data_length + index_length), 0) as size
		FROM information_schema.tables
		WHERE table_schema = ?
	`
	err = m.db.QueryRowContext(ctx, sizeQuery, m.config.Database).Scan(&size)
	if err != nil {
		size.Int64 = 0
	}

	// 获取表数量
	var tableCount int
	tableQuery := `
		SELECT COUNT(*)
		FROM information_schema.tables
		WHERE table_schema = ?
	`
	err = m.db.QueryRowContext(ctx, tableQuery, m.config.Database).Scan(&tableCount)
	if err != nil {
		tableCount = 0
	}

	return &model.DatabaseInfo{
		Name:    m.config.Database,
		Size:    size.Int64,
		Tables:  tableCount,
		Version: version,
	}, nil
}

// GetTables 获取表列表
func (m *MySQLRepository) GetTables(ctx context.Context, database string) ([]*model.TableInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MySQL")
	}

	query := `
		SELECT
			table_name,
			table_type,
			IFNULL(table_rows, 0) as table_rows,
			IFNULL(data_length + index_length, 0) as table_size,
			IFNULL(table_comment, '') as table_comment
		FROM information_schema.tables
		WHERE table_schema = ?
		ORDER BY table_name
	`

	rows, err := m.db.QueryContext(ctx, query, database)
	if err != nil {
		return nil, fmt.Errorf("failed to query tables: %w", err)
	}
	defer rows.Close()

	var tables []*model.TableInfo
	for rows.Next() {
		var table model.TableInfo
		err := rows.Scan(
			&table.Name,
			&table.Type,
			&table.Rows,
			&table.Size,
			&table.Comment,
		)
		if err != nil {
			continue
		}
		tables = append(tables, &table)
	}

	return tables, nil
}

// GetColumns 获取表列信息
func (m *MySQLRepository) GetColumns(ctx context.Context, database, table string) ([]*model.ColumnInfo, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MySQL")
	}

	query := `
		SELECT
			column_name,
			data_type,
			is_nullable,
			IFNULL(column_default, '') as column_default,
			IFNULL(column_comment, '') as column_comment,
			column_key,
			extra
		FROM information_schema.columns
		WHERE table_schema = ? AND table_name = ?
		ORDER BY ordinal_position
	`

	rows, err := m.db.QueryContext(ctx, query, database, table)
	if err != nil {
		return nil, fmt.Errorf("failed to query columns: %w", err)
	}
	defer rows.Close()

	var columns []*model.ColumnInfo
	for rows.Next() {
		var column model.ColumnInfo
		var nullable, columnKey, extra string

		err := rows.Scan(
			&column.Name,
			&column.Type,
			&nullable,
			&column.DefaultValue,
			&column.Comment,
			&columnKey,
			&extra,
		)
		if err != nil {
			continue
		}

		column.Nullable = nullable == "YES"
		column.IsPrimaryKey = columnKey == "PRI"
		column.IsAutoIncrement = strings.Contains(extra, "auto_increment")

		columns = append(columns, &column)
	}

	return columns, nil
}

// ExecuteQuery 执行查询
func (m *MySQLRepository) ExecuteQuery(ctx context.Context, query string, params ...any) (*model.QueryResult, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MySQL")
	}

	start := time.Now()

	// 执行查询
	rows, err := m.db.QueryContext(ctx, query, params...)
	if err != nil {
		return &model.QueryResult{
			Columns: []string{"Error"},
			Rows:    [][]any{},
			Total:   0,
			Elapsed: time.Since(start),
			Error:   err.Error(),
		}, nil
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	// 读取数据
	var resultRows [][]any
	for rows.Next() {
		// 创建扫描目标
		values := make([]any, len(columns))
		valuePtrs := make([]any, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描行数据
		if err := rows.Scan(valuePtrs...); err != nil {
			continue
		}

		// 处理NULL值
		row := make([]any, len(columns))
		for i, val := range values {
			if val == nil {
				row[i] = nil
			} else {
				switch v := val.(type) {
				case []byte:
					row[i] = string(v)
				default:
					row[i] = v
				}
			}
		}

		resultRows = append(resultRows, row)
	}

	return &model.QueryResult{
		Columns: columns,
		Rows:    resultRows,
		Total:   int64(len(resultRows)),
		Elapsed: time.Since(start),
	}, nil
}

// ExecuteCommand 执行命令
func (m *MySQLRepository) ExecuteCommand(ctx context.Context, command string, params ...any) error {
	if !m.IsConnected() {
		return fmt.Errorf("not connected to MySQL")
	}

	var err error
	if m.tx != nil {
		// 在事务中执行
		_, err = m.tx.ExecContext(ctx, command, params...)
	} else {
		// 直接执行
		_, err = m.db.ExecContext(ctx, command, params...)
	}

	return err
}

// BeginTransaction 开始事务
func (m *MySQLRepository) BeginTransaction(ctx context.Context) error {
	if !m.IsConnected() {
		return fmt.Errorf("not connected to MySQL")
	}

	if m.tx != nil {
		return fmt.Errorf("transaction already started")
	}

	tx, err := m.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	m.tx = tx
	return nil
}

// CommitTransaction 提交事务
func (m *MySQLRepository) CommitTransaction(ctx context.Context) error {
	if m.tx == nil {
		return fmt.Errorf("no active transaction")
	}

	err := m.tx.Commit()
	m.tx = nil
	return err
}

// RollbackTransaction 回滚事务
func (m *MySQLRepository) RollbackTransaction(ctx context.Context) error {
	if m.tx == nil {
		return fmt.Errorf("no active transaction")
	}

	err := m.tx.Rollback()
	m.tx = nil
	return err
}

// GetSchemas 获取模式列表
func (m *MySQLRepository) GetSchemas(ctx context.Context) ([]string, error) {
	if !m.IsConnected() {
		return nil, fmt.Errorf("not connected to MySQL")
	}

	query := "SHOW DATABASES"
	rows, err := m.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query schemas: %w", err)
	}
	defer rows.Close()

	var schemas []string
	for rows.Next() {
		var schema string
		if err := rows.Scan(&schema); err != nil {
			continue
		}
		// 过滤系统数据库
		if schema != "information_schema" && schema != "performance_schema" && schema != "mysql" && schema != "sys" {
			schemas = append(schemas, schema)
		}
	}

	return schemas, nil
}
