package version

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"time"
)

// 版本信息常量
var (
	Version   = "1.0.0"           // 应用版本
	BuildTime = "unknown"         // 构建时间
	GitCommit = "unknown"         // Git提交哈希
	GitBranch = "unknown"         // Git分支
	GoVersion = runtime.Version() // Go版本
)

// Info 版本信息结构
type Info struct {
	Version     string `json:"version"`
	BuildTime   string `json:"buildTime"`
	GitCommit   string `json:"gitCommit"`
	GitBranch   string `json:"gitBranch"`
	GoVersion   string `json:"goVersion"`
	Platform    string `json:"platform"`
	Architecture string `json:"architecture"`
}

// GetInfo 获取版本信息
func GetInfo() *Info {
	return &Info{
		Version:      Version,
		BuildTime:    BuildTime,
		GitCommit:    GitCommit,
		GitBranch:    GitBranch,
		GoVersion:    GoVersion,
		Platform:     runtime.GOOS,
		Architecture: runtime.GOARCH,
	}
}

// String 返回版本信息字符串
func (i *Info) String() string {
	return fmt.Sprintf("Database Manager %s (%s/%s) built at %s from %s@%s with %s",
		i.Version, i.Platform, i.Architecture, i.BuildTime, i.GitBranch, i.GitCommit, i.GoVersion)
}

// UpdateInfo 更新信息结构
type UpdateInfo struct {
	Available     bool      `json:"available"`
	LatestVersion string    `json:"latestVersion"`
	CurrentVersion string   `json:"currentVersion"`
	ReleaseNotes  string    `json:"releaseNotes"`
	DownloadURL   string    `json:"downloadUrl"`
	ReleaseDate   time.Time `json:"releaseDate"`
	Critical      bool      `json:"critical"`
}

// GitHubRelease GitHub发布信息
type GitHubRelease struct {
	TagName     string    `json:"tag_name"`
	Name        string    `json:"name"`
	Body        string    `json:"body"`
	PublishedAt time.Time `json:"published_at"`
	Assets      []struct {
		Name               string `json:"name"`
		BrowserDownloadURL string `json:"browser_download_url"`
	} `json:"assets"`
	Prerelease bool `json:"prerelease"`
	Draft      bool `json:"draft"`
}

// UpdateChecker 更新检查器
type UpdateChecker struct {
	repoURL     string
	currentVersion string
	httpClient  *http.Client
}

// NewUpdateChecker 创建更新检查器
func NewUpdateChecker(repoURL string) *UpdateChecker {
	return &UpdateChecker{
		repoURL:        repoURL,
		currentVersion: Version,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CheckForUpdates 检查更新
func (uc *UpdateChecker) CheckForUpdates() (*UpdateInfo, error) {
	// 获取最新发布信息
	release, err := uc.getLatestRelease()
	if err != nil {
		return nil, fmt.Errorf("failed to get latest release: %w", err)
	}

	// 比较版本
	isNewer, err := uc.isVersionNewer(release.TagName, uc.currentVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to compare versions: %w", err)
	}

	// 查找适合当前平台的下载链接
	downloadURL := uc.findDownloadURL(release)

	updateInfo := &UpdateInfo{
		Available:      isNewer,
		LatestVersion:  release.TagName,
		CurrentVersion: uc.currentVersion,
		ReleaseNotes:   release.Body,
		DownloadURL:    downloadURL,
		ReleaseDate:    release.PublishedAt,
		Critical:       uc.isCriticalUpdate(release.Body),
	}

	return updateInfo, nil
}

// getLatestRelease 获取最新发布信息
func (uc *UpdateChecker) getLatestRelease() (*GitHubRelease, error) {
	url := fmt.Sprintf("%s/releases/latest", uc.repoURL)
	
	resp, err := uc.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	var release GitHubRelease
	if err := json.NewDecoder(resp.Body).Decode(&release); err != nil {
		return nil, err
	}

	// 跳过预发布和草稿
	if release.Prerelease || release.Draft {
		return nil, fmt.Errorf("latest release is prerelease or draft")
	}

	return &release, nil
}

// isVersionNewer 比较版本号
func (uc *UpdateChecker) isVersionNewer(newVersion, currentVersion string) (bool, error) {
	// 移除版本号前缀 'v'
	newVersion = strings.TrimPrefix(newVersion, "v")
	currentVersion = strings.TrimPrefix(currentVersion, "v")

	newParts := strings.Split(newVersion, ".")
	currentParts := strings.Split(currentVersion, ".")

	// 确保版本号部分数量一致
	maxLen := len(newParts)
	if len(currentParts) > maxLen {
		maxLen = len(currentParts)
	}

	// 补齐版本号部分
	for len(newParts) < maxLen {
		newParts = append(newParts, "0")
	}
	for len(currentParts) < maxLen {
		currentParts = append(currentParts, "0")
	}

	// 逐个比较版本号部分
	for i := 0; i < maxLen; i++ {
		newPart, err := strconv.Atoi(newParts[i])
		if err != nil {
			return false, fmt.Errorf("invalid version format: %s", newVersion)
		}

		currentPart, err := strconv.Atoi(currentParts[i])
		if err != nil {
			return false, fmt.Errorf("invalid version format: %s", currentVersion)
		}

		if newPart > currentPart {
			return true, nil
		} else if newPart < currentPart {
			return false, nil
		}
	}

	return false, nil // 版本相同
}

// findDownloadURL 查找适合当前平台的下载链接
func (uc *UpdateChecker) findDownloadURL(release *GitHubRelease) string {
	platform := runtime.GOOS
	arch := runtime.GOARCH

	// 平台特定的文件名模式
	patterns := []string{
		fmt.Sprintf("database-manager-%s-%s", platform, arch),
		fmt.Sprintf("database-manager-%s", platform),
		"database-manager-setup.exe", // Windows安装程序
		"Database Manager.dmg",       // macOS DMG
		"database-manager.deb",       // Linux DEB
		"database-manager.AppImage",  // Linux AppImage
	}

	for _, asset := range release.Assets {
		for _, pattern := range patterns {
			if strings.Contains(strings.ToLower(asset.Name), strings.ToLower(pattern)) {
				return asset.BrowserDownloadURL
			}
		}
	}

	// 如果没有找到特定平台的文件，返回第一个资源
	if len(release.Assets) > 0 {
		return release.Assets[0].BrowserDownloadURL
	}

	return ""
}

// isCriticalUpdate 判断是否为关键更新
func (uc *UpdateChecker) isCriticalUpdate(releaseNotes string) bool {
	criticalKeywords := []string{
		"critical", "security", "vulnerability", "urgent",
		"hotfix", "emergency", "important security",
	}

	lowerNotes := strings.ToLower(releaseNotes)
	for _, keyword := range criticalKeywords {
		if strings.Contains(lowerNotes, keyword) {
			return true
		}
	}

	return false
}

// GetVersionString 获取简短版本字符串
func GetVersionString() string {
	return Version
}

// GetFullVersionString 获取完整版本字符串
func GetFullVersionString() string {
	info := GetInfo()
	return info.String()
}

// IsDevVersion 判断是否为开发版本
func IsDevVersion() bool {
	return strings.Contains(Version, "dev") || 
		   strings.Contains(Version, "alpha") || 
		   strings.Contains(Version, "beta") || 
		   strings.Contains(Version, "rc")
}
