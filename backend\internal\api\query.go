package api

import (
	"context"
	"database-manager/backend/internal/repository"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
)

// QueryAPI 数据查询API
type QueryAPI struct {
	connectionManager *repository.ConnectionManager
}

// NewQueryAPI 创建数据查询API
func NewQueryAPI(connectionManager *repository.ConnectionManager) *QueryAPI {
	return &QueryAPI{
		connectionManager: connectionManager,
	}
}

// ExecuteQueryRequest 执行查询请求
type ExecuteQueryRequest struct {
	ConnectionID string   `json:"connection_id"`
	Query        string   `json:"query"`
	Params       []string `json:"params"`
}

// GetDatabaseInfoRequest 获取数据库信息请求
type GetDatabaseInfoRequest struct {
	ConnectionID string `json:"connection_id"`
}

// GetTablesRequest 获取表列表请求
type GetTablesRequest struct {
	ConnectionID string `json:"connection_id"`
	Database     string `json:"database"`
}

// GetColumnsRequest 获取列信息请求
type GetColumnsRequest struct {
	ConnectionID string `json:"connection_id"`
	Database     string `json:"database"`
	Table        string `json:"table"`
}

// ExecuteQuery 执行查询
func (api *QueryAPI) ExecuteQuery(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req ExecuteQueryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 验证请求
	if req.ConnectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if req.Query == "" {
		api.writeError(w, http.StatusBadRequest, "Query is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), req.ConnectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 转换参数
	params := make([]any, len(req.Params))
	for i, param := range req.Params {
		params[i] = param
	}

	// 执行查询
	result, err := conn.ExecuteQuery(context.Background(), req.Query, params...)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Query execution failed: %v", err))
		return
	}

	api.writeSuccess(w, result, "Query executed successfully")
}

// GetDatabaseInfo 获取数据库信息
func (api *QueryAPI) GetDatabaseInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从查询参数获取连接ID
	connectionID := r.URL.Query().Get("connection_id")
	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), connectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 获取数据库信息
	info, err := conn.GetDatabaseInfo(context.Background())
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get database info: %v", err))
		return
	}

	api.writeSuccess(w, info, "")
}

// GetTables 获取表列表
func (api *QueryAPI) GetTables(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从查询参数获取参数
	connectionID := r.URL.Query().Get("connection_id")
	database := r.URL.Query().Get("database")

	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), connectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 获取表列表
	tables, err := conn.GetTables(context.Background(), database)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get tables: %v", err))
		return
	}

	api.writeSuccess(w, tables, "")
}

// GetColumns 获取列信息
func (api *QueryAPI) GetColumns(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从查询参数获取参数
	connectionID := r.URL.Query().Get("connection_id")
	database := r.URL.Query().Get("database")
	table := r.URL.Query().Get("table")

	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if table == "" {
		api.writeError(w, http.StatusBadRequest, "Table name is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), connectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 获取列信息
	columns, err := conn.GetColumns(context.Background(), database, table)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get columns: %v", err))
		return
	}

	api.writeSuccess(w, columns, "")
}

// GetSchemas 获取模式列表 (适用于PostgreSQL等)
func (api *QueryAPI) GetSchemas(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从查询参数获取连接ID
	connectionID := r.URL.Query().Get("connection_id")
	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), connectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否支持模式
	sqlConn, ok := conn.(repository.SQLConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Database does not support schemas")
		return
	}

	// 获取模式列表
	schemas, err := sqlConn.GetSchemas(context.Background())
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get schemas: %v", err))
		return
	}

	api.writeSuccess(w, schemas, "")
}

// GetRedisKeys 获取Redis键列表
func (api *QueryAPI) GetRedisKeys(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从查询参数获取参数
	connectionID := r.URL.Query().Get("connection_id")
	pattern := r.URL.Query().Get("pattern")
	limitStr := r.URL.Query().Get("limit")

	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}

	// 解析limit参数
	limit := 100 // 默认限制
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), connectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为Redis连接
	redisConn, ok := conn.(repository.RedisConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a Redis database")
		return
	}

	// 获取键列表
	keys, err := redisConn.GetKeys(context.Background(), pattern, limit)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get Redis keys: %v", err))
		return
	}

	api.writeSuccess(w, keys, "")
}

// GetRedisKeyInfo 获取Redis键信息
func (api *QueryAPI) GetRedisKeyInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从查询参数获取参数
	connectionID := r.URL.Query().Get("connection_id")
	key := r.URL.Query().Get("key")

	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if key == "" {
		api.writeError(w, http.StatusBadRequest, "Key is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), connectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为Redis连接
	redisConn, ok := conn.(repository.RedisConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a Redis database")
		return
	}

	// 获取键信息
	keyInfo, err := redisConn.GetKeyInfo(context.Background(), key)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get Redis key info: %v", err))
		return
	}

	api.writeSuccess(w, keyInfo, "")
}

// GetMongoCollections 获取MongoDB集合列表
func (api *QueryAPI) GetMongoCollections(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从查询参数获取参数
	connectionID := r.URL.Query().Get("connection_id")
	database := r.URL.Query().Get("database")

	if connectionID == "" {
		api.writeError(w, http.StatusBadRequest, "Connection ID is required")
		return
	}
	if database == "" {
		api.writeError(w, http.StatusBadRequest, "Database name is required")
		return
	}

	// 获取数据库连接
	conn, err := api.connectionManager.GetConnection(context.Background(), connectionID)
	if err != nil {
		api.writeError(w, http.StatusBadRequest, fmt.Sprintf("Failed to get connection: %v", err))
		return
	}

	// 检查是否为MongoDB连接
	mongoConn, ok := conn.(repository.MongoConnector)
	if !ok {
		api.writeError(w, http.StatusBadRequest, "Connection is not a MongoDB database")
		return
	}

	// 获取集合列表
	collections, err := mongoConn.GetCollections(context.Background(), database)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get MongoDB collections: %v", err))
		return
	}

	api.writeSuccess(w, collections, "")
}

// 辅助方法

// writeSuccess 写入成功响应
func (api *QueryAPI) writeSuccess(w http.ResponseWriter, data interface{}, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := APIResponse{
		Success: true,
		Data:    data,
		Message: message,
	}

	json.NewEncoder(w).Encode(response)
}

// writeError 写入错误响应
func (api *QueryAPI) writeError(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := APIResponse{
		Success: false,
		Error:   message,
	}

	json.NewEncoder(w).Encode(response)
}
