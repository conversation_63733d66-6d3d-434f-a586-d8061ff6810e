import React, { useState, useEffect } from 'react';
import { Button, Input, Modal } from '../common';

interface Connection {
  id: string;
  name: string;
  type: string;
  host: string;
  port: number;
  database: string;
}

interface RedisKey {
  key: string;
  type: string;
  ttl: number;
  size?: number;
  value?: any;
}

interface RedisManagerProps {
  connection: Connection;
}

const RedisManager: React.FC<RedisManagerProps> = ({ connection }) => {
  const [keys, setKeys] = useState<RedisKey[]>([]);
  const [filteredKeys, setFilteredKeys] = useState<RedisKey[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());
  const [selectedKey, setSelectedKey] = useState<string>('');
  const [keyValue, setKeyValue] = useState<any>(null);
  const [searchPattern, setSearchPattern] = useState<string>('*');
  const [currentDatabase, setCurrentDatabase] = useState<number>(parseInt(connection.database) || 0);
  const [loading, setLoading] = useState(false);
  const [keyTree, setKeyTree] = useState<any>({});
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingKey, setEditingKey] = useState<RedisKey | null>(null);
  const [newValue, setNewValue] = useState<string>('');
  const [newTTL, setNewTTL] = useState<number>(-1);

  // 数据库列表 (Redis 默认 16 个数据库)
  const databases = Array.from({ length: 16 }, (_, i) => i);

  useEffect(() => {
    loadKeys();
  }, [currentDatabase, searchPattern]);

  useEffect(() => {
    buildKeyTree();
    filterKeys();
  }, [keys, searchPattern]);

  // 加载键列表
  const loadKeys = async () => {
    try {
      setLoading(true);
      
      // 切换数据库
      if (currentDatabase !== parseInt(connection.database)) {
        await (window as any).go.main.App.ExecuteQuery(connection.id, `SELECT ${currentDatabase}`);
      }

      // 获取键列表
      const result = await (window as any).go.main.App.ExecuteQuery(connection.id, `KEYS ${searchPattern}`);
      const keyNames = result.rows?.map((row: any) => row[0] || row) || [];

      // 获取每个键的详细信息
      const keyDetails = await Promise.all(
        keyNames.map(async (keyName: string) => {
          try {
            const [typeResult, ttlResult] = await Promise.all([
              (window as any).go.main.App.ExecuteQuery(connection.id, `TYPE ${keyName}`),
              (window as any).go.main.App.ExecuteQuery(connection.id, `TTL ${keyName}`)
            ]);

            return {
              key: keyName,
              type: typeResult.rows?.[0]?.[0] || 'string',
              ttl: parseInt(ttlResult.rows?.[0]?.[0]) || -1,
            };
          } catch (error) {
            return {
              key: keyName,
              type: 'unknown',
              ttl: -1,
            };
          }
        })
      );

      setKeys(keyDetails);
    } catch (error) {
      console.error('Failed to load Redis keys:', error);
    } finally {
      setLoading(false);
    }
  };

  // 构建键树结构
  const buildKeyTree = () => {
    const tree: any = {};
    
    keys.forEach(keyInfo => {
      const parts = keyInfo.key.split(':');
      let current = tree;
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        
        if (i === parts.length - 1) {
          // 叶子节点（实际的键）
          current[part] = {
            ...keyInfo,
            isLeaf: true
          };
        } else {
          // 文件夹节点
          if (!current[part]) {
            current[part] = {
              isFolder: true,
              children: {},
              count: 0
            };
          }
          current[part].count = (current[part].count || 0) + 1;
          current = current[part].children = current[part].children || {};
        }
      }
    });
    
    setKeyTree(tree);
  };

  // 过滤键
  const filterKeys = () => {
    if (searchPattern === '*') {
      setFilteredKeys(keys);
    } else {
      const pattern = searchPattern.replace(/\*/g, '.*').replace(/\?/g, '.');
      const regex = new RegExp(pattern, 'i');
      setFilteredKeys(keys.filter(key => regex.test(key.key)));
    }
  };

  // 切换数据库
  const switchDatabase = async (dbIndex: number) => {
    try {
      await (window as any).go.main.App.ExecuteQuery(connection.id, `SELECT ${dbIndex}`);
      setCurrentDatabase(dbIndex);
      setSelectedKey('');
      setKeyValue(null);
      setSelectedKeys(new Set());
    } catch (error) {
      console.error('Failed to switch database:', error);
    }
  };

  // 获取键值
  const loadKeyValue = async (keyName: string) => {
    try {
      setLoading(true);
      const keyInfo = keys.find(k => k.key === keyName);
      if (!keyInfo) return;

      let value;
      switch (keyInfo.type) {
        case 'string':
          const stringResult = await (window as any).go.main.App.ExecuteQuery(connection.id, `GET ${keyName}`);
          value = stringResult.rows?.[0]?.[0] || '';
          break;
        case 'list':
          const listResult = await (window as any).go.main.App.ExecuteQuery(connection.id, `LRANGE ${keyName} 0 -1`);
          value = listResult.rows?.map((row: any) => row[0]) || [];
          break;
        case 'set':
          const setResult = await (window as any).go.main.App.ExecuteQuery(connection.id, `SMEMBERS ${keyName}`);
          value = setResult.rows?.map((row: any) => row[0]) || [];
          break;
        case 'hash':
          const hashResult = await (window as any).go.main.App.ExecuteQuery(connection.id, `HGETALL ${keyName}`);
          const hashData: any = {};
          const hashRows = hashResult.rows || [];
          for (let i = 0; i < hashRows.length; i += 2) {
            if (hashRows[i] && hashRows[i + 1]) {
              hashData[hashRows[i][0]] = hashRows[i + 1][0];
            }
          }
          value = hashData;
          break;
        case 'zset':
          const zsetResult = await (window as any).go.main.App.ExecuteQuery(connection.id, `ZRANGE ${keyName} 0 -1 WITHSCORES`);
          const zsetData: any[] = [];
          const zsetRows = zsetResult.rows || [];
          for (let i = 0; i < zsetRows.length; i += 2) {
            if (zsetRows[i] && zsetRows[i + 1]) {
              zsetData.push({
                member: zsetRows[i][0],
                score: parseFloat(zsetRows[i + 1][0])
              });
            }
          }
          value = zsetData;
          break;
        default:
          value = 'Unsupported type';
      }

      setKeyValue(value);
      setSelectedKey(keyName);
    } catch (error) {
      console.error('Failed to load key value:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除键
  const deleteKey = async (keyName: string) => {
    if (!confirm(`Are you sure you want to delete key "${keyName}"?`)) return;
    
    try {
      await (window as any).go.main.App.ExecuteQuery(connection.id, `DEL ${keyName}`);
      await loadKeys();
      if (selectedKey === keyName) {
        setSelectedKey('');
        setKeyValue(null);
      }
    } catch (error) {
      console.error('Failed to delete key:', error);
    }
  };

  // 批量删除
  const batchDelete = async () => {
    if (selectedKeys.size === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedKeys.size} keys?`)) return;
    
    try {
      const keyList = Array.from(selectedKeys).join(' ');
      await (window as any).go.main.App.ExecuteQuery(connection.id, `DEL ${keyList}`);
      await loadKeys();
      setSelectedKeys(new Set());
      setSelectedKey('');
      setKeyValue(null);
    } catch (error) {
      console.error('Failed to batch delete keys:', error);
    }
  };

  // 编辑键值
  const editKey = (keyInfo: RedisKey) => {
    setEditingKey(keyInfo);
    setNewTTL(keyInfo.ttl);
    
    if (keyInfo.key === selectedKey && keyValue !== null) {
      if (typeof keyValue === 'string') {
        setNewValue(keyValue);
      } else {
        setNewValue(JSON.stringify(keyValue, null, 2));
      }
    } else {
      setNewValue('');
    }
    
    setShowEditModal(true);
  };

  // 保存编辑
  const saveEdit = async () => {
    if (!editingKey) return;
    
    try {
      // 更新值
      if (editingKey.type === 'string') {
        await (window as any).go.main.App.ExecuteQuery(connection.id, `SET ${editingKey.key} "${newValue}"`);
      }
      
      // 更新TTL
      if (newTTL > 0) {
        await (window as any).go.main.App.ExecuteQuery(connection.id, `EXPIRE ${editingKey.key} ${newTTL}`);
      } else if (newTTL === 0) {
        await (window as any).go.main.App.ExecuteQuery(connection.id, `PERSIST ${editingKey.key}`);
      }
      
      setShowEditModal(false);
      await loadKeys();
      if (selectedKey === editingKey.key) {
        await loadKeyValue(editingKey.key);
      }
    } catch (error) {
      console.error('Failed to save changes:', error);
    }
  };

  // 切换键选择
  const toggleKeySelection = (keyName: string) => {
    const newSelected = new Set(selectedKeys);
    if (newSelected.has(keyName)) {
      newSelected.delete(keyName);
    } else {
      newSelected.add(keyName);
    }
    setSelectedKeys(newSelected);
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedKeys.size === filteredKeys.length) {
      setSelectedKeys(new Set());
    } else {
      setSelectedKeys(new Set(filteredKeys.map(k => k.key)));
    }
  };

  // 切换文件夹展开状态
  const toggleFolder = (folderPath: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderPath)) {
      newExpanded.delete(folderPath);
    } else {
      newExpanded.add(folderPath);
    }
    setExpandedFolders(newExpanded);
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900">
      {/* 顶部工具栏 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Redis Manager - {connection.name}
          </h2>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Database:</span>
            <select
              value={currentDatabase}
              onChange={(e) => switchDatabase(parseInt(e.target.value))}
              className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              {databases.map(db => (
                <option key={db} value={db}>DB {db}</option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <Input
              placeholder="Search pattern (e.g., user:*, session:*)"
              value={searchPattern}
              onChange={(e) => setSearchPattern(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && loadKeys()}
            />
          </div>
          <Button onClick={loadKeys} disabled={loading}>
            {loading ? 'Loading...' : 'Search'}
          </Button>
          <Button 
            onClick={batchDelete} 
            variant="danger" 
            disabled={selectedKeys.size === 0}
          >
            Delete Selected ({selectedKeys.size})
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧键列表 */}
        <div className="w-1/3 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Keys ({filteredKeys.length})
              </span>
              <label className="flex items-center text-sm">
                <input
                  type="checkbox"
                  checked={selectedKeys.size === filteredKeys.length && filteredKeys.length > 0}
                  onChange={toggleSelectAll}
                  className="mr-2"
                />
                Select All
              </label>
            </div>
          </div>
          
          <div className="flex-1 overflow-auto">
            {filteredKeys.map((keyInfo) => (
              <div
                key={keyInfo.key}
                className={`p-3 border-b border-gray-100 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
                  selectedKey === keyInfo.key ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
                onClick={() => loadKeyValue(keyInfo.key)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <input
                      type="checkbox"
                      checked={selectedKeys.has(keyInfo.key)}
                      onChange={(e) => {
                        e.stopPropagation();
                        toggleKeySelection(keyInfo.key);
                      }}
                      className="flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {keyInfo.key}
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                          keyInfo.type === 'string' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          keyInfo.type === 'list' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          keyInfo.type === 'set' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                          keyInfo.type === 'hash' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          keyInfo.type === 'zset' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                        }`}>
                          {keyInfo.type}
                        </span>
                        <span>
                          TTL: {keyInfo.ttl === -1 ? '∞' : keyInfo.ttl === -2 ? 'N/A' : `${keyInfo.ttl}s`}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 flex-shrink-0">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        editKey(keyInfo);
                      }}
                    >
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteKey(keyInfo.key);
                      }}
                    >
                      Del
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧值显示区域 */}
        <div className="flex-1 flex flex-col">
          {selectedKey ? (
            <>
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {selectedKey}
                </h3>
                <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                  <span>Type: {keys.find(k => k.key === selectedKey)?.type}</span>
                  <span>TTL: {keys.find(k => k.key === selectedKey)?.ttl === -1 ? 'No expiration' : `${keys.find(k => k.key === selectedKey)?.ttl}s`}</span>
                </div>
              </div>
              
              <div className="flex-1 p-4 overflow-auto">
                {keyValue !== null ? (
                  <div className="space-y-4">
                    {typeof keyValue === 'string' ? (
                      <textarea
                        value={keyValue}
                        readOnly
                        className="w-full h-64 p-3 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm"
                      />
                    ) : Array.isArray(keyValue) ? (
                      <div className="space-y-2">
                        {keyValue.map((item, index) => (
                          <div key={index} className="p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                            {typeof item === 'object' ? (
                              <div className="flex justify-between">
                                <span className="font-mono">{item.member}</span>
                                <span className="text-gray-500">Score: {item.score}</span>
                              </div>
                            ) : (
                              <span className="font-mono">{item}</span>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : typeof keyValue === 'object' ? (
                      <div className="space-y-2">
                        {Object.entries(keyValue).map(([field, value]) => (
                          <div key={field} className="p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                            <div className="flex justify-between">
                              <span className="font-medium">{field}:</span>
                              <span className="font-mono">{String(value)}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <pre className="bg-gray-50 dark:bg-gray-800 p-3 rounded border font-mono text-sm">
                        {JSON.stringify(keyValue, null, 2)}
                      </pre>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 dark:text-gray-400 mt-8">
                    Loading value...
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500 dark:text-gray-400">
              Select a key to view its value
            </div>
          )}
        </div>
      </div>

      {/* 编辑模态框 */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={`Edit Key: ${editingKey?.key}`}
        size="lg"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Value
            </label>
            <textarea
              value={newValue}
              onChange={(e) => setNewValue(e.target.value)}
              className="w-full h-32 p-3 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm"
              placeholder="Enter new value..."
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              TTL (seconds)
            </label>
            <Input
              type="number"
              value={newTTL.toString()}
              onChange={(e) => setNewTTL(parseInt(e.target.value) || -1)}
              placeholder="Enter TTL in seconds (-1 for no expiration)"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              -1 = No expiration, 0 = Remove expiration, &gt;0 = Expire in N seconds
            </p>
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button onClick={saveEdit}>
              Save Changes
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default RedisManager;
